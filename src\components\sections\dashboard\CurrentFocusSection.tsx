import { motion } from 'framer-motion'
import { Folder, BookOpen } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { BaseSectionProps } from './types'
import { renderIcon } from './utils'
import { currentlyLearning, workingOn } from './data'

export default function CurrentFocusSection({ className }: BaseSectionProps) {
  return (
    <section className={`py-20 lg:py-32 ${className || ''}`}>
      {/* Header Section - Matching FeaturedWorkSection */}
      <div className="container mx-auto px-4 text-center mb-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="mb-2"
        >
          <span className="text-caption font-medium text-muted-foreground tracking-wider uppercase">
            CURRENT FOCUS
          </span>
        </motion.div>
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight mb-6"
        >
          Current{' '}
          <span className="relative font-apparel font-black italic -skew-x-12 inline-block bg-gradient-to-r from-purple-500 via-pink-500 via-orange-500 via-blue-500 to-purple-500 bg-[length:400%_400%] bg-clip-text text-transparent animate-gradient-shift [filter:drop-shadow(0_0_15px_rgba(148,56,213,0.5))_drop-shadow(0_0_1.5px_rgba(0,0,0,0.2))]">
            focus
          </span>
        </motion.h2>
      </div>

      {/* Enhanced Content Grid */}
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16">
          {/* What I'm Building - Enhanced layout */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="group"
          >
            <div className="relative">
              <div className="bg-neutral-900/50 backdrop-blur-lg shadow-[inset_0_0_20px_0_rgba(255,255,255,0.08)] border border-neutral-700/50 rounded-3xl p-8 hover:border-neutral-600/50 transition-all duration-300">
                <div className="mb-8">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-14 h-14 bg-gradient-to-br from-purple-500/20 via-pink-500/20 to-orange-500/20 rounded-2xl flex items-center justify-center border border-purple-500/20">
                      <Folder className="w-7 h-7 text-purple-500" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-foreground">What I'm Building</h3>
                      <p className="text-muted-foreground text-sm">Current projects and ongoing development</p>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  {workingOn.map((project, index) => (
                    <motion.div
                      key={project.name}
                      initial={{ opacity: 0, y: 10 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.4, delay: 0.3 + index * 0.1 }}
                      className="flex items-center justify-between p-5 rounded-2xl hover:bg-muted/5 transition-all duration-300 group/item border border-border/10 hover:border-border/30"
                    >
                      <div className="flex items-center gap-4">
                        {renderIcon(project.icon, cn(
                          "w-5 h-5 transition-all duration-300",
                          project.status === "Active" ? "text-green-500" : "text-purple-500"
                        ))}
                        <span className="font-semibold text-foreground group-hover/item:text-primary transition-colors duration-300">
                          {project.name}
                        </span>
                      </div>
                      <Badge
                        variant={project.status === "Active" ? "default" : "secondary"}
                        className={cn(
                          "text-xs px-4 py-2 font-semibold rounded-full",
                          project.status === "Active"
                            ? "bg-green-500/15 text-green-600 border-green-500/30"
                            : "bg-purple-500/15 text-purple-600 border-purple-500/30"
                        )}
                      >
                        {project.status}
                      </Badge>
                    </motion.div>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>

          {/* What I'm Learning - Enhanced layout */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="group"
          >
            <div className="relative">
              <div className="bg-neutral-900/50 backdrop-blur-lg shadow-[inset_0_0_20px_0_rgba(255,255,255,0.08)] border border-neutral-700/50 rounded-3xl p-8 hover:border-neutral-600/50 transition-all duration-300">
                <div className="mb-8">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-14 h-14 bg-gradient-to-br from-blue-500/20 via-teal-500/20 to-green-500/20 rounded-2xl flex items-center justify-center border border-blue-500/20">
                      <BookOpen className="w-7 h-7 text-blue-500" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-foreground">What I'm Learning</h3>
                      <p className="text-muted-foreground text-sm">Technologies and concepts I'm actively studying</p>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  {currentlyLearning.map((item, index) => (
                    <motion.div
                      key={item.name}
                      initial={{ opacity: 0, y: 10 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.4, delay: 0.5 + index * 0.1 }}
                      className="flex items-center gap-5 p-4 rounded-xl hover:bg-muted/5 transition-all duration-300 group/item border border-border/10 hover:border-border/30"
                    >
                      {renderIcon(item.icon, "w-5 h-5 text-primary/80 group-hover/item:text-primary transition-colors duration-300")}
                      <span className="font-semibold text-foreground group-hover/item:text-primary transition-colors duration-300">
                        {item.name}
                      </span>
                    </motion.div>
                  ))}
                </div>

                <div className="mt-8 pt-6 border-t border-border/20">
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    Continuously expanding my skillset to stay current with modern web development practices.
                  </p>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
