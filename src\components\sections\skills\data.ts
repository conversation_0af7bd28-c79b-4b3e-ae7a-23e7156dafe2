import {
  <PERSON>,
  Palette,
  Wrench,
  <PERSON>,
  Book<PERSON>pen,
  Star,
  TrendingUp,
  Award,
  Target,
  Zap,
  Brain,
  Lightbulb,
  Rocket,
  Calendar,
  Clock,
  CheckCircle,
  Circle,
  Database,
  Globe,
  Smartphone,
  Monitor,
  GitBranch,
  Settings,
  Layers,
  PenTool,
  Coffee,
  Heart,
  Eye,
  MessageSquare,
  Users2,
  Timer,
  Focus,
  Repeat,
  Sparkles,
  Server,
  Cloud,
  Shield,
  TestTube,
  Paintbrush,
  Layout,
  Code2,
  FileCode,
  Workflow
} from "lucide-react"
import {
  SiReact,
  SiTypescript,
  SiNextdotjs,
  SiTailwindcss,
  SiJavascript,
  SiGit,
  SiHtml5,
  SiCss3,
  SiNodedotjs,
  SiPostgresql,
  SiPrisma,
  SiFigma,
  SiSass,
  SiStyledcomponents,
  SiFramer,
  SiVitest,
  SiGraphql,
  SiVuedotjs,
  SiExpress,
  SiMongodb,
  SiRedis,
  SiDocker,
  SiAws,
  SiVercel,
  SiJest,
  SiCypress,
  SiWebpack,
  SiVite,
  SiEslint,
  Si<PERSON>rettier,
  SiSupabase,
  SiFirebase,
  SiStripe,
  SiAuth0
} from "react-icons/si"
import { TechnicalSkill, SoftSkill, LearningGoal, CategoryFilter, RoadmapNode, RoadmapSection, SkillStatus } from './types'

// Enhanced technical skills data structure
export const technicalSkills: TechnicalSkill[] = [
  {
    name: "React",
    level: 95,
    category: "Frontend",
    icon: SiReact,
    experience: "3+ years",
    description: "Advanced component architecture, hooks, context, and performance optimization",
    projects: 15,
    color: "#61DAFB"
  },
  {
    name: "TypeScript",
    level: 90,
    category: "Frontend",
    icon: SiTypescript,
    experience: "2+ years",
    description: "Strong typing, advanced types, generics, and type-safe development",
    projects: 12,
    color: "#3178C6"
  },
  {
    name: "Next.js",
    level: 85,
    category: "Frontend",
    icon: SiNextdotjs,
    experience: "2+ years",
    description: "App Router, SSR, SSG, API routes, and performance optimization",
    projects: 8,
    color: "#000000"
  },
  {
    name: "JavaScript",
    level: 95,
    category: "Frontend",
    icon: SiJavascript,
    experience: "4+ years",
    description: "ES6+, async/await, closures, prototypes, and modern JavaScript patterns",
    projects: 20,
    color: "#F7DF1E"
  },
  {
    name: "HTML5",
    level: 98,
    category: "Frontend",
    icon: SiHtml5,
    experience: "5+ years",
    description: "Semantic markup, accessibility, web standards, and modern HTML features",
    projects: 25,
    color: "#E34F26"
  },
  {
    name: "CSS3",
    level: 95,
    category: "Styling",
    icon: SiCss3,
    experience: "5+ years",
    description: "Flexbox, Grid, animations, responsive design, and modern CSS features",
    projects: 25,
    color: "#1572B6"
  },
  {
    name: "Tailwind CSS",
    level: 92,
    category: "Styling",
    icon: SiTailwindcss,
    experience: "2+ years",
    description: "Utility-first CSS, custom configurations, and component design systems",
    projects: 15,
    color: "#06B6D4"
  },
  {
    name: "Sass/SCSS",
    level: 88,
    category: "Styling",
    icon: SiSass,
    experience: "3+ years",
    description: "Variables, mixins, functions, and modular CSS architecture",
    projects: 10,
    color: "#CC6699"
  },
  {
    name: "Styled Components",
    level: 80,
    category: "Styling",
    icon: SiStyledcomponents,
    experience: "1+ years",
    description: "CSS-in-JS, dynamic styling, and component-based styling",
    projects: 6,
    color: "#DB7093"
  },
  {
    name: "Git",
    level: 90,
    category: "Tools",
    icon: SiGit,
    experience: "4+ years",
    description: "Version control, branching strategies, merge conflicts, and collaboration",
    projects: 25,
    color: "#F05032"
  },
  {
    name: "VS Code",
    level: 95,
    category: "Tools",
    icon: Settings,
    experience: "4+ years",
    description: "Extensions, debugging, integrated terminal, and productivity optimization",
    projects: 25,
    color: "#007ACC"
  },
  {
    name: "Figma",
    level: 85,
    category: "Design",
    icon: SiFigma,
    experience: "2+ years",
    description: "UI/UX design, prototyping, design systems, and developer handoff",
    projects: 12,
    color: "#F24E1E"
  },
  {
    name: "Node.js",
    level: 75,
    category: "Backend",
    icon: SiNodedotjs,
    experience: "1+ years",
    description: "Server-side JavaScript, Express.js, API development, and npm packages",
    projects: 5,
    color: "#339933"
  },
  {
    name: "PostgreSQL",
    level: 70,
    category: "Database",
    icon: SiPostgresql,
    experience: "1+ years",
    description: "Relational databases, SQL queries, and database design",
    projects: 4,
    color: "#336791"
  },
  {
    name: "Prisma",
    level: 75,
    category: "Database",
    icon: SiPrisma,
    experience: "1+ years",
    description: "ORM, database migrations, and type-safe database access",
    projects: 4,
    color: "#2D3748"
  },
  {
    name: "Framer Motion",
    level: 88,
    category: "Animation",
    icon: SiFramer,
    experience: "2+ years",
    description: "Complex animations, gestures, layout animations, and micro-interactions",
    projects: 10,
    color: "#0055FF"
  },
  {
    name: "Jest",
    level: 80,
    category: "Testing",
    icon: SiVitest,
    experience: "1+ years",
    description: "Unit testing, mocking, and test-driven development practices",
    projects: 8,
    color: "#C21325"
  },
  {
    name: "React Testing Library",
    level: 75,
    category: "Testing",
    icon: SiReact,
    experience: "1+ years",
    description: "Component testing, user interaction testing, and accessibility testing",
    projects: 6,
    color: "#61DAFB"
  },
  {
    name: "Express.js",
    level: 78,
    category: "Backend",
    icon: SiNodedotjs,
    experience: "1+ years",
    description: "RESTful API development, middleware, authentication, and error handling",
    projects: 6,
    color: "#339933"
  }
]

// Soft skills with detailed descriptions
export const softSkills: SoftSkill[] = [
  {
    name: "Problem Solving",
    icon: Brain,
    description: "Breaking down complex problems into manageable solutions",
    examples: ["Debugging complex issues", "Optimizing performance", "Architecture decisions"],
    strength: 95
  },
  {
    name: "Critical Thinking",
    icon: Lightbulb,
    description: "Analyzing situations objectively and making informed decisions",
    examples: ["Code reviews", "Technical planning", "Solution evaluation"],
    strength: 90
  },
  {
    name: "Communication",
    icon: MessageSquare,
    description: "Clear technical communication with team members and stakeholders",
    examples: ["Documentation", "Code comments", "Team presentations"],
    strength: 88
  },
  {
    name: "Team Collaboration",
    icon: Users2,
    description: "Working effectively in cross-functional teams and pair programming",
    examples: ["Agile workflows", "Code collaboration", "Knowledge sharing"],
    strength: 92
  },
  {
    name: "Time Management",
    icon: Timer,
    description: "Prioritizing tasks and meeting project deadlines consistently",
    examples: ["Sprint planning", "Task prioritization", "Deadline management"],
    strength: 90
  },
  {
    name: "Attention to Detail",
    icon: Eye,
    description: "Ensuring code quality, catching bugs, and maintaining standards",
    examples: ["Code quality", "Testing", "Standards compliance"],
    strength: 95
  },
  {
    name: "Adaptability",
    icon: Repeat,
    description: "Quickly learning new technologies and adapting to changing requirements",
    examples: ["New frameworks", "Changing requirements", "Technology updates"],
    strength: 88
  },
  {
    name: "Learning Agility",
    icon: TrendingUp,
    description: "Continuously improving skills and staying current with technology trends",
    examples: ["Online courses", "Documentation reading", "Experimentation"],
    strength: 95
  }
]

// Currently learning with progress and timeline
export const currentlyLearning: LearningGoal[] = [
  {
    name: "Advanced React Patterns",
    progress: 75,
    icon: SiReact,
    timeline: "3 months",
    description: "Compound components, render props, higher-order components",
    priority: "High",
    resources: ["Epic React", "React docs", "Advanced patterns course"]
  },
  {
    name: "Frontend Testing",
    progress: 60,
    icon: SiVitest,
    timeline: "4 months",
    description: "Unit testing, integration testing, and E2E testing strategies",
    priority: "High",
    resources: ["Testing Library docs", "Vitest documentation", "Testing courses"]
  },
  {
    name: "GraphQL",
    progress: 40,
    icon: SiGraphql,
    timeline: "2 months",
    description: "Query language, Apollo Client, and modern data fetching",
    priority: "Medium",
    resources: ["GraphQL docs", "Apollo tutorials", "Practical GraphQL"]
  },
  {
    name: "Vue.js",
    progress: 30,
    icon: SiVuedotjs,
    timeline: "1 month",
    description: "Alternative frontend framework and ecosystem exploration",
    priority: "Low",
    resources: ["Vue.js docs", "Vue mastery", "Composition API guide"]
  },
  {
    name: "Backend Development",
    progress: 50,
    icon: SiNodedotjs,
    timeline: "6 months",
    description: "Full-stack development with Node.js and database management",
    priority: "Medium",
    resources: ["Node.js docs", "Express guides", "Database design patterns"]
  },
  {
    name: "Web Performance",
    progress: 70,
    icon: Rocket,
    timeline: "2 months",
    description: "Core Web Vitals, optimization techniques, and performance monitoring",
    priority: "High",
    resources: ["Web.dev", "Performance guides", "Lighthouse documentation"]
  }
]

// Skill categories for filtering - reorganized for full-stack focus
export const skillCategories: CategoryFilter[] = [
  { name: "All", icon: Globe, count: technicalSkills.length },
  { name: "Frontend", icon: Monitor, count: technicalSkills.filter(s => s.category === "Frontend" || s.category === "Styling" || s.category === "Animation").length },
  { name: "Backend", icon: Database, count: technicalSkills.filter(s => s.category === "Backend").length },
  { name: "Database & DevOps", icon: Settings, count: technicalSkills.filter(s => s.category === "Database" || s.category === "Tools").length },
  { name: "Testing", icon: CheckCircle, count: technicalSkills.filter(s => s.category === "Testing").length }
]

// Comprehensive Full-Stack Developer Roadmap Data
export const roadmapSections: RoadmapSection[] = [
  {
    id: 'fundamentals',
    title: 'Web Fundamentals',
    description: 'Essential building blocks of web development',
    color: '#E34F26',
    position: { startY: 0, endY: 200 },
    nodes: [
      {
        id: 'html',
        name: 'HTML5',
        description: 'Semantic markup, accessibility, and modern HTML features',
        icon: SiHtml5,
        status: 'completed',
        level: 98,
        experience: '5+ years',
        projects: 25,
        color: '#E34F26',
        category: 'Frontend',
        position: { x: 100, y: 50 },
        prerequisites: [],
        unlocks: ['css', 'accessibility'],
        resources: {
          documentation: ['MDN HTML Reference', 'HTML5 Specification'],
          courses: ['HTML & CSS Course', 'Web Accessibility Course'],
          projects: ['Personal Portfolio', 'Landing Pages', 'Form Validation'],
          articles: ['Semantic HTML Guide', 'HTML Best Practices']
        },
        keyTopics: ['Semantic Elements', 'Forms', 'Accessibility', 'SEO', 'Meta Tags'],
        estimatedTime: '2-3 weeks',
        difficulty: 'Beginner'
      },
      {
        id: 'css',
        name: 'CSS3',
        description: 'Flexbox, Grid, animations, responsive design, and modern CSS features',
        icon: SiCss3,
        status: 'completed',
        level: 95,
        experience: '5+ years',
        projects: 25,
        color: '#1572B6',
        category: 'Styling',
        position: { x: 300, y: 50 },
        prerequisites: ['html'],
        unlocks: ['sass', 'tailwind', 'responsive-design'],
        resources: {
          documentation: ['MDN CSS Reference', 'CSS Grid Guide', 'Flexbox Guide'],
          courses: ['CSS Grid Course', 'Flexbox Course', 'CSS Animations'],
          projects: ['Responsive Layouts', 'CSS Art', 'Animation Demos'],
          articles: ['Modern CSS Techniques', 'CSS Architecture']
        },
        keyTopics: ['Flexbox', 'Grid', 'Animations', 'Responsive Design', 'CSS Variables'],
        estimatedTime: '4-6 weeks',
        difficulty: 'Beginner'
      },
      {
        id: 'javascript',
        name: 'JavaScript',
        description: 'ES6+, async/await, closures, prototypes, and modern JavaScript patterns',
        icon: SiJavascript,
        status: 'completed',
        level: 95,
        experience: '4+ years',
        projects: 20,
        color: '#F7DF1E',
        category: 'Frontend',
        position: { x: 500, y: 50 },
        prerequisites: ['html', 'css'],
        unlocks: ['typescript', 'react', 'nodejs'],
        resources: {
          documentation: ['MDN JavaScript', 'JavaScript.info', 'ECMAScript Spec'],
          courses: ['JavaScript: The Complete Guide', 'ES6+ Features', 'Async JavaScript'],
          projects: ['DOM Manipulation', 'API Integration', 'Interactive Games'],
          articles: ['You Don\'t Know JS', 'JavaScript Patterns']
        },
        keyTopics: ['ES6+', 'Async/Await', 'Closures', 'Prototypes', 'Event Loop'],
        estimatedTime: '8-12 weeks',
        difficulty: 'Intermediate'
      }
    ]
  },
  {
    id: 'frontend-frameworks',
    title: 'Frontend Frameworks',
    description: 'Modern JavaScript frameworks and libraries',
    color: '#61DAFB',
    position: { startY: 250, endY: 450 },
    nodes: [
      {
        id: 'react',
        name: 'React',
        description: 'Advanced component architecture, hooks, context, and performance optimization',
        icon: SiReact,
        status: 'completed',
        level: 95,
        experience: '3+ years',
        projects: 15,
        color: '#61DAFB',
        category: 'Frontend',
        position: { x: 200, y: 300 },
        prerequisites: ['javascript'],
        unlocks: ['nextjs', 'react-testing', 'state-management'],
        resources: {
          documentation: ['React Docs', 'React Patterns', 'React DevTools'],
          courses: ['React Complete Guide', 'Advanced React', 'React Hooks'],
          projects: ['Todo App', 'E-commerce Site', 'Social Media App'],
          articles: ['React Best Practices', 'Performance Optimization']
        },
        keyTopics: ['Components', 'Hooks', 'Context', 'Performance', 'Patterns'],
        estimatedTime: '6-8 weeks',
        difficulty: 'Intermediate'
      },
      {
        id: 'typescript',
        name: 'TypeScript',
        description: 'Strong typing, advanced types, generics, and type-safe development',
        icon: SiTypescript,
        status: 'completed',
        level: 90,
        experience: '2+ years',
        projects: 12,
        color: '#3178C6',
        category: 'Frontend',
        position: { x: 400, y: 300 },
        prerequisites: ['javascript'],
        unlocks: ['nextjs', 'advanced-typescript'],
        resources: {
          documentation: ['TypeScript Handbook', 'Type Challenges', 'TS Config'],
          courses: ['TypeScript Complete Guide', 'Advanced TypeScript'],
          projects: ['Type-safe API', 'Generic Utilities', 'Complex Types'],
          articles: ['TypeScript Best Practices', 'Advanced Types']
        },
        keyTopics: ['Types', 'Interfaces', 'Generics', 'Utility Types', 'Decorators'],
        estimatedTime: '4-6 weeks',
        difficulty: 'Intermediate'
      },
      {
        id: 'nextjs',
        name: 'Next.js',
        description: 'App Router, SSR, SSG, API routes, and performance optimization',
        icon: SiNextdotjs,
        status: 'completed',
        level: 85,
        experience: '2+ years',
        projects: 8,
        color: '#000000',
        category: 'Frontend',
        position: { x: 600, y: 300 },
        prerequisites: ['react', 'typescript'],
        unlocks: ['fullstack-apps', 'deployment'],
        resources: {
          documentation: ['Next.js Docs', 'App Router Guide', 'API Routes'],
          courses: ['Next.js Complete Guide', 'Full-Stack Next.js'],
          projects: ['Blog Platform', 'E-commerce', 'Dashboard'],
          articles: ['Next.js Best Practices', 'Performance Tips']
        },
        keyTopics: ['App Router', 'SSR/SSG', 'API Routes', 'Middleware', 'Optimization'],
        estimatedTime: '4-6 weeks',
        difficulty: 'Advanced'
      }
    ]
  },
  {
    id: 'styling-tools',
    title: 'Styling & Design',
    description: 'CSS frameworks, preprocessors, and design tools',
    color: '#06B6D4',
    position: { startY: 500, endY: 650 },
    nodes: [
      {
        id: 'tailwind',
        name: 'Tailwind CSS',
        description: 'Utility-first CSS, custom configurations, and component design systems',
        icon: SiTailwindcss,
        status: 'completed',
        level: 92,
        experience: '2+ years',
        projects: 15,
        color: '#06B6D4',
        category: 'Styling',
        position: { x: 150, y: 550 },
        prerequisites: ['css'],
        unlocks: ['design-systems'],
        resources: {
          documentation: ['Tailwind Docs', 'Tailwind UI', 'Headless UI'],
          courses: ['Tailwind CSS Course', 'Design Systems'],
          projects: ['Component Library', 'Landing Pages', 'Dashboards'],
          articles: ['Tailwind Best Practices', 'Custom Configurations']
        },
        keyTopics: ['Utility Classes', 'Responsive Design', 'Dark Mode', 'Plugins', 'JIT'],
        estimatedTime: '2-3 weeks',
        difficulty: 'Beginner'
      },
      {
        id: 'sass',
        name: 'Sass/SCSS',
        description: 'Variables, mixins, functions, and modular CSS architecture',
        icon: SiSass,
        status: 'completed',
        level: 88,
        experience: '3+ years',
        projects: 10,
        color: '#CC6699',
        category: 'Styling',
        position: { x: 350, y: 550 },
        prerequisites: ['css'],
        unlocks: ['css-architecture'],
        resources: {
          documentation: ['Sass Documentation', 'Sass Guidelines'],
          courses: ['Sass Complete Guide', 'CSS Architecture'],
          projects: ['Modular Stylesheets', 'Theme Systems'],
          articles: ['Sass Best Practices', 'SCSS vs Sass']
        },
        keyTopics: ['Variables', 'Mixins', 'Functions', 'Partials', 'Architecture'],
        estimatedTime: '2-3 weeks',
        difficulty: 'Intermediate'
      },
      {
        id: 'figma',
        name: 'Figma',
        description: 'UI/UX design, prototyping, design systems, and developer handoff',
        icon: SiFigma,
        status: 'completed',
        level: 85,
        experience: '2+ years',
        projects: 12,
        color: '#F24E1E',
        category: 'Design',
        position: { x: 550, y: 550 },
        prerequisites: [],
        unlocks: ['design-systems', 'prototyping'],
        resources: {
          documentation: ['Figma Help Center', 'Design Systems Guide'],
          courses: ['Figma Masterclass', 'UI/UX Design'],
          projects: ['Design System', 'Mobile App Design', 'Web Prototypes'],
          articles: ['Design Handoff', 'Component Design']
        },
        keyTopics: ['Components', 'Auto Layout', 'Prototyping', 'Design Systems', 'Collaboration'],
        estimatedTime: '3-4 weeks',
        difficulty: 'Beginner'
      }
    ]
  },
  {
    id: 'backend-development',
    title: 'Backend Development',
    description: 'Server-side development and APIs',
    color: '#339933',
    position: { startY: 700, endY: 900 },
    nodes: [
      {
        id: 'nodejs',
        name: 'Node.js',
        description: 'Server-side JavaScript, Express.js, API development, and npm packages',
        icon: SiNodedotjs,
        status: 'in-progress',
        level: 75,
        experience: '1+ years',
        projects: 5,
        color: '#339933',
        category: 'Backend',
        position: { x: 200, y: 750 },
        prerequisites: ['javascript'],
        unlocks: ['express', 'apis', 'fullstack'],
        resources: {
          documentation: ['Node.js Docs', 'NPM Documentation'],
          courses: ['Node.js Complete Guide', 'Backend Development'],
          projects: ['REST API', 'File Server', 'Real-time Chat'],
          articles: ['Node.js Best Practices', 'Event Loop']
        },
        keyTopics: ['Event Loop', 'Modules', 'File System', 'Streams', 'NPM'],
        estimatedTime: '4-6 weeks',
        difficulty: 'Intermediate'
      },
      {
        id: 'express',
        name: 'Express.js',
        description: 'RESTful API development, middleware, authentication, and error handling',
        icon: SiExpress,
        status: 'in-progress',
        level: 78,
        experience: '1+ years',
        projects: 6,
        color: '#000000',
        category: 'Backend',
        position: { x: 400, y: 750 },
        prerequisites: ['nodejs'],
        unlocks: ['apis', 'authentication'],
        resources: {
          documentation: ['Express.js Docs', 'Middleware Guide'],
          courses: ['Express.js Course', 'API Development'],
          projects: ['REST API', 'Authentication System', 'CRUD App'],
          articles: ['Express Best Practices', 'Security Guide']
        },
        keyTopics: ['Routing', 'Middleware', 'Error Handling', 'Security', 'Testing'],
        estimatedTime: '3-4 weeks',
        difficulty: 'Intermediate'
      },
      {
        id: 'postgresql',
        name: 'PostgreSQL',
        description: 'Relational databases, SQL queries, and database design',
        icon: SiPostgresql,
        status: 'available',
        level: 70,
        experience: '1+ years',
        projects: 4,
        color: '#336791',
        category: 'Database',
        position: { x: 600, y: 750 },
        prerequisites: [],
        unlocks: ['prisma', 'database-design'],
        resources: {
          documentation: ['PostgreSQL Docs', 'SQL Tutorial'],
          courses: ['PostgreSQL Course', 'Database Design'],
          projects: ['E-commerce DB', 'Analytics DB', 'User Management'],
          articles: ['PostgreSQL Best Practices', 'Query Optimization']
        },
        keyTopics: ['SQL Queries', 'Indexes', 'Relationships', 'Transactions', 'Performance'],
        estimatedTime: '4-6 weeks',
        difficulty: 'Intermediate'
      }
    ]
  },
  {
    id: 'database-tools',
    title: 'Database & Tools',
    description: 'Database management and development tools',
    color: '#2D3748',
    position: { startY: 950, endY: 1100 },
    nodes: [
      {
        id: 'prisma',
        name: 'Prisma',
        description: 'ORM, database migrations, and type-safe database access',
        icon: SiPrisma,
        status: 'available',
        level: 75,
        experience: '1+ years',
        projects: 4,
        color: '#2D3748',
        category: 'Database',
        position: { x: 200, y: 1000 },
        prerequisites: ['postgresql'],
        unlocks: ['fullstack-apps'],
        resources: {
          documentation: ['Prisma Docs', 'Schema Reference', 'Client API'],
          courses: ['Prisma Complete Guide', 'Database with Prisma'],
          projects: ['Blog with Prisma', 'E-commerce Backend', 'User Management'],
          articles: ['Prisma Best Practices', 'Migration Strategies']
        },
        keyTopics: ['Schema Design', 'Migrations', 'Client API', 'Relations', 'Queries'],
        estimatedTime: '2-3 weeks',
        difficulty: 'Intermediate'
      },
      {
        id: 'git',
        name: 'Git',
        description: 'Version control, branching strategies, merge conflicts, and collaboration',
        icon: SiGit,
        status: 'completed',
        level: 90,
        experience: '4+ years',
        projects: 25,
        color: '#F05032',
        category: 'Tools',
        position: { x: 400, y: 1000 },
        prerequisites: [],
        unlocks: ['collaboration', 'deployment'],
        resources: {
          documentation: ['Git Documentation', 'Pro Git Book', 'Git Workflows'],
          courses: ['Git Complete Guide', 'Advanced Git', 'Git for Teams'],
          projects: ['Open Source Contributions', 'Team Projects', 'Git Workflows'],
          articles: ['Git Best Practices', 'Branching Strategies']
        },
        keyTopics: ['Branching', 'Merging', 'Rebasing', 'Workflows', 'Collaboration'],
        estimatedTime: '3-4 weeks',
        difficulty: 'Intermediate'
      },
      {
        id: 'vscode',
        name: 'VS Code',
        description: 'Extensions, debugging, integrated terminal, and productivity optimization',
        icon: Settings,
        status: 'completed',
        level: 95,
        experience: '4+ years',
        projects: 25,
        color: '#007ACC',
        category: 'Tools',
        position: { x: 600, y: 1000 },
        prerequisites: [],
        unlocks: ['productivity'],
        resources: {
          documentation: ['VS Code Docs', 'Extension API', 'Debugging Guide'],
          courses: ['VS Code Mastery', 'Extension Development'],
          projects: ['Custom Extensions', 'Workspace Setup', 'Debugging Configs'],
          articles: ['VS Code Tips', 'Productivity Hacks']
        },
        keyTopics: ['Extensions', 'Debugging', 'Terminal', 'Shortcuts', 'Customization'],
        estimatedTime: '1-2 weeks',
        difficulty: 'Beginner'
      }
    ]
  },
  {
    id: 'testing-deployment',
    title: 'Testing & Deployment',
    description: 'Testing strategies and deployment platforms',
    color: '#C21325',
    position: { startY: 1150, endY: 1300 },
    nodes: [
      {
        id: 'testing',
        name: 'Testing',
        description: 'Unit testing, integration testing, and E2E testing strategies',
        icon: SiVitest,
        status: 'in-progress',
        level: 75,
        experience: '1+ years',
        projects: 6,
        color: '#C21325',
        category: 'Testing',
        position: { x: 200, y: 1200 },
        prerequisites: ['react', 'javascript'],
        unlocks: ['quality-assurance'],
        resources: {
          documentation: ['Vitest Docs', 'Testing Library', 'Jest Documentation'],
          courses: ['Testing JavaScript', 'React Testing', 'E2E Testing'],
          projects: ['Test Suites', 'TDD Projects', 'Integration Tests'],
          articles: ['Testing Best Practices', 'Testing Strategies']
        },
        keyTopics: ['Unit Tests', 'Integration Tests', 'E2E Tests', 'Mocking', 'TDD'],
        estimatedTime: '4-6 weeks',
        difficulty: 'Intermediate'
      },
      {
        id: 'deployment',
        name: 'Deployment',
        description: 'Cloud platforms, CI/CD, and production deployment strategies',
        icon: SiVercel,
        status: 'available',
        level: 70,
        experience: '1+ years',
        projects: 8,
        color: '#000000',
        category: 'DevOps',
        position: { x: 400, y: 1200 },
        prerequisites: ['nextjs', 'git'],
        unlocks: ['production-apps'],
        resources: {
          documentation: ['Vercel Docs', 'Netlify Docs', 'AWS Docs'],
          courses: ['Deployment Strategies', 'CI/CD Pipelines', 'Cloud Platforms'],
          projects: ['Production Deployments', 'CI/CD Setup', 'Monitoring'],
          articles: ['Deployment Best Practices', 'Performance Monitoring']
        },
        keyTopics: ['CI/CD', 'Cloud Platforms', 'Environment Variables', 'Monitoring', 'Scaling'],
        estimatedTime: '3-4 weeks',
        difficulty: 'Advanced'
      },
      {
        id: 'performance',
        name: 'Performance',
        description: 'Core Web Vitals, optimization techniques, and performance monitoring',
        icon: Rocket,
        status: 'in-progress',
        level: 70,
        experience: '2+ years',
        projects: 10,
        color: '#FF6B35',
        category: 'Optimization',
        position: { x: 600, y: 1200 },
        prerequisites: ['react', 'nextjs'],
        unlocks: ['advanced-optimization'],
        resources: {
          documentation: ['Web.dev', 'Lighthouse Docs', 'Performance API'],
          courses: ['Web Performance', 'Core Web Vitals', 'Optimization Techniques'],
          projects: ['Performance Audits', 'Optimization Projects', 'Monitoring Setup'],
          articles: ['Performance Best Practices', 'Optimization Strategies']
        },
        keyTopics: ['Core Web Vitals', 'Bundle Optimization', 'Image Optimization', 'Caching', 'Monitoring'],
        estimatedTime: '4-6 weeks',
        difficulty: 'Advanced'
      }
    ]
  }
]

// Roadmap connections showing skill dependencies
export const roadmapConnections: RoadmapConnection[] = [
  // Fundamentals flow
  { from: 'html', to: 'css', type: 'prerequisite' },
  { from: 'css', to: 'javascript', type: 'prerequisite' },

  // Frontend framework flow
  { from: 'javascript', to: 'react', type: 'prerequisite' },
  { from: 'javascript', to: 'typescript', type: 'recommended' },
  { from: 'react', to: 'nextjs', type: 'prerequisite' },
  { from: 'typescript', to: 'nextjs', type: 'recommended' },

  // Styling flow
  { from: 'css', to: 'tailwind', type: 'prerequisite' },
  { from: 'css', to: 'sass', type: 'prerequisite' },

  // Backend flow
  { from: 'javascript', to: 'nodejs', type: 'prerequisite' },
  { from: 'nodejs', to: 'express', type: 'prerequisite' },

  // Database flow
  { from: 'postgresql', to: 'prisma', type: 'prerequisite' },

  // Testing flow
  { from: 'react', to: 'testing', type: 'recommended' },
  { from: 'javascript', to: 'testing', type: 'prerequisite' },

  // Deployment flow
  { from: 'nextjs', to: 'deployment', type: 'recommended' },
  { from: 'git', to: 'deployment', type: 'prerequisite' },

  // Performance flow
  { from: 'react', to: 'performance', type: 'recommended' },
  { from: 'nextjs', to: 'performance', type: 'recommended' },

  // Cross-connections
  { from: 'react', to: 'tailwind', type: 'recommended' },
  { from: 'nextjs', to: 'nodejs', type: 'recommended' },
  { from: 'express', to: 'postgresql', type: 'recommended' },
  { from: 'express', to: 'prisma', type: 'recommended' },
  { from: 'figma', to: 'tailwind', type: 'optional' },
  { from: 'figma', to: 'react', type: 'optional' }
]
