import React from 'react'
import { motion } from 'framer-motion'
import { CheckCircle, Clock, Lock, Circle } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { RoadmapNode, SkillStatus } from './types'
import { renderIcon } from './utils'

interface SkillNodeComponentProps {
  node: RoadmapNode
  isHovered: boolean
  onHover: (nodeId: string | null) => void
  onClick: (node: RoadmapNode) => void
}

export default function SkillNodeComponent({
  node,
  isHovered,
  onHover,
  onClick
}: SkillNodeComponentProps) {
  
  // Get status icon and color
  const getStatusIcon = (status: SkillStatus) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'in-progress':
        return <Clock className="w-4 h-4 text-yellow-500" />
      case 'available':
        return <Circle className="w-4 h-4 text-gray-400" />
      case 'locked':
        return <Lock className="w-4 h-4 text-gray-600" />
      default:
        return <Circle className="w-4 h-4 text-gray-400" />
    }
  }

  const getStatusColor = (status: SkillStatus) => {
    switch (status) {
      case 'completed': return 'from-green-500/20 to-green-600/20 border-green-500/50'
      case 'in-progress': return 'from-yellow-500/20 to-yellow-600/20 border-yellow-500/50'
      case 'available': return 'from-blue-500/20 to-blue-600/20 border-blue-500/50'
      case 'locked': return 'from-gray-500/20 to-gray-600/20 border-gray-500/30'
      default: return 'from-gray-500/20 to-gray-600/20 border-gray-500/30'
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner': return 'bg-green-500/20 text-green-300 border-green-500/30'
      case 'Intermediate': return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30'
      case 'Advanced': return 'bg-orange-500/20 text-orange-300 border-orange-500/30'
      case 'Expert': return 'bg-red-500/20 text-red-300 border-red-500/30'
      default: return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
    }
  }

  const isInteractive = node.status !== 'locked'

  return (
    <motion.g
      initial={{ opacity: 0, scale: 0.8, y: 20 }}
      animate={{ opacity: 1, scale: 1, y: 0 }}
      transition={{
        duration: 0.5,
        type: "spring",
        stiffness: 100,
        delay: Math.random() * 0.3 // Stagger animation randomly
      }}
      whileHover={isInteractive ? { scale: 1.05 } : {}}
      style={{ cursor: isInteractive ? 'pointer' : 'not-allowed' }}
    >
      {/* Glow Effect for In-Progress Nodes */}
      {node.status === 'in-progress' && (
        <motion.rect
          x={node.position.x - 2}
          y={node.position.y - 2}
          width={124}
          height={84}
          rx={14}
          fill="none"
          stroke="#F59E0B"
          strokeWidth={2}
          opacity={0.6}
          animate={{
            opacity: [0.3, 0.8, 0.3],
            strokeWidth: [1, 3, 1]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      )}

      {/* Node Background */}
      <motion.rect
        x={node.position.x}
        y={node.position.y}
        width={120}
        height={80}
        rx={12}
        className={cn(
          'fill-current transition-all duration-300',
          `bg-gradient-to-br ${getStatusColor(node.status)}`,
          isHovered && isInteractive && 'drop-shadow-lg'
        )}
        style={{
          fill: isHovered && isInteractive
            ? 'rgba(59, 130, 246, 0.1)'
            : 'rgba(30, 41, 59, 0.8)'
        }}
        stroke={isHovered && isInteractive ? '#3B82F6' : 'rgba(148, 163, 184, 0.3)'}
        strokeWidth={isHovered && isInteractive ? 2 : 1}
        onMouseEnter={() => isInteractive && onHover(node.id)}
        onMouseLeave={() => onHover(null)}
        onClick={() => isInteractive && onClick(node)}
        whileHover={isInteractive ? {
          scale: 1.05,
          boxShadow: "0 10px 25px rgba(0,0,0,0.3)"
        } : {}}
        whileTap={isInteractive ? { scale: 0.95 } : {}}
        animate={node.status === 'completed' ? {
          boxShadow: [
            "0 0 0 rgba(16, 185, 129, 0)",
            "0 0 20px rgba(16, 185, 129, 0.3)",
            "0 0 0 rgba(16, 185, 129, 0)"
          ]
        } : {}}
        transition={node.status === 'completed' ? {
          duration: 3,
          repeat: Infinity,
          ease: "easeInOut"
        } : {}}
      />

      {/* Status Indicator */}
      <motion.g
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ delay: 0.2 }}
      >
        <circle
          cx={node.position.x + 110}
          cy={node.position.y + 10}
          r={8}
          className="fill-slate-800 stroke-slate-600"
          strokeWidth={1}
        />
        <foreignObject
          x={node.position.x + 106}
          y={node.position.y + 6}
          width={8}
          height={8}
        >
          {getStatusIcon(node.status)}
        </foreignObject>
      </motion.g>

      {/* Skill Icon */}
      <foreignObject
        x={node.position.x + 8}
        y={node.position.y + 8}
        width={24}
        height={24}
      >
        <div className="w-6 h-6 flex items-center justify-center">
          {renderIcon(node.icon, "w-5 h-5 text-white")}
        </div>
      </foreignObject>

      {/* Skill Name */}
      <text
        x={node.position.x + 40}
        y={node.position.y + 20}
        className="fill-white text-sm font-semibold"
        style={{ fontSize: '12px' }}
      >
        {node.name}
      </text>

      {/* Experience */}
      <text
        x={node.position.x + 40}
        y={node.position.y + 35}
        className="fill-slate-300 text-xs"
        style={{ fontSize: '10px' }}
      >
        {node.experience}
      </text>

      {/* Progress Bar */}
      {node.status !== 'locked' && (
        <motion.g
          initial={{ scaleX: 0 }}
          animate={{ scaleX: 1 }}
          transition={{ delay: 0.4, duration: 0.6 }}
        >
          {/* Background */}
          <rect
            x={node.position.x + 8}
            y={node.position.y + 45}
            width={104}
            height={4}
            rx={2}
            className="fill-slate-700"
          />
          {/* Progress */}
          <rect
            x={node.position.x + 8}
            y={node.position.y + 45}
            width={(104 * node.level) / 100}
            height={4}
            rx={2}
            style={{
              fill: node.status === 'completed' ? '#10B981' : 
                    node.status === 'in-progress' ? '#F59E0B' : '#6B7280'
            }}
          />
        </motion.g>
      )}

      {/* Level Badge */}
      <foreignObject
        x={node.position.x + 8}
        y={node.position.y + 55}
        width={40}
        height={20}
      >
        <Badge
          variant="secondary"
          className="text-xs px-2 py-0 bg-slate-700/50 text-slate-300 border-slate-600"
        >
          {node.level}%
        </Badge>
      </foreignObject>

      {/* Difficulty Badge */}
      <foreignObject
        x={node.position.x + 55}
        y={node.position.y + 55}
        width={55}
        height={20}
      >
        <Badge
          variant="outline"
          className={cn("text-xs px-2 py-0", getDifficultyColor(node.difficulty))}
        >
          {node.difficulty}
        </Badge>
      </foreignObject>

      {/* Hover Tooltip */}
      {isHovered && isInteractive && (
        <motion.g
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 10 }}
        >
          <rect
            x={node.position.x - 20}
            y={node.position.y - 40}
            width={160}
            height={30}
            rx={6}
            className="fill-slate-800 stroke-slate-600"
            strokeWidth={1}
            style={{ filter: 'drop-shadow(0 4px 6px rgba(0, 0, 0, 0.3))' }}
          />
          <text
            x={node.position.x + 60}
            y={node.position.y - 20}
            className="fill-white text-xs text-center"
            textAnchor="middle"
            style={{ fontSize: '11px' }}
          >
            Click to learn more
          </text>
        </motion.g>
      )}
    </motion.g>
  )
}
