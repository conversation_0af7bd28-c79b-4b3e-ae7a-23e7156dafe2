import { LucideIcon } from 'lucide-react'
import { IconType } from 'react-icons'

// Base interface for all skills sections
export interface BaseSectionProps {
  className?: string
}

// Technical skill interface
export interface TechnicalSkill {
  name: string
  level: number
  category: string
  icon: IconType | LucideIcon
  experience: string
  description: string
  projects: number
  color: string
}

// Soft skill interface
export interface SoftSkill {
  name: string
  icon: LucideIcon
  description: string
  examples: string[]
  strength: number
}

// Learning goal interface
export interface LearningGoal {
  name: string
  icon: IconType | LucideIcon
  description: string
  timeline: string
  priority: "High" | "Medium" | "Low"
  progress: number
  resources: string[]
}

// Category filter interface
export interface CategoryFilter {
  name: string
  icon: LucideIcon
  count: number
}

// Roadmap-specific types
export type SkillStatus = 'completed' | 'in-progress' | 'available' | 'locked'

export interface RoadmapNode {
  id: string
  name: string
  description: string
  icon: IconType | LucideIcon
  status: SkillStatus
  level: number
  experience: string
  projects: number
  color: string
  category: string
  position: {
    x: number
    y: number
  }
  prerequisites: string[]
  unlocks: string[]
  resources: {
    documentation: string[]
    courses: string[]
    projects: string[]
    articles: string[]
  }
  keyTopics: string[]
  estimatedTime: string
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced' | 'Expert'
}

export interface RoadmapSection {
  id: string
  title: string
  description: string
  color: string
  nodes: RoadmapNode[]
  position: {
    startY: number
    endY: number
  }
}

export interface RoadmapConnection {
  from: string
  to: string
  type: 'prerequisite' | 'recommended' | 'optional'
}

// Animation variants
export const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1
    }
  }
}

export const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4
    }
  }
}

export const staggerContainerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.08,
      delayChildren: 0.1
    }
  }
}
