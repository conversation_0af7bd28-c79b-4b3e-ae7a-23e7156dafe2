// src/components/sections/HeroSection.tsx

import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { Copy, Check } from 'lucide-react';
import React, { useState } from 'react';
import InteractiveTerminal from '@/components/InteractiveTerminal';
import { scrollToTopInstant } from '@/components/ScrollToTop';
// The original <Button> component is still used for "Let's Connect"
import { Button } from '@/components/ui/button';
import { BaseSectionProps, containerVariants } from './types';
import { createNavigationHandler } from './utils';
import { userData } from '@/data/personal/userData';

export default function HeroSection({ className }: BaseSectionProps) {
  const navigate = useNavigate();
  const handleNavigation = createNavigationHandler(navigate, scrollToTopInstant);
  const [emailCopied, setEmailCopied] = useState(false);

  const handleCopyEmail = (e: React.MouseEvent<HTMLButtonElement>) => {
    // Stop the event from bubbling up, just in case.
    e.stopPropagation();
    
    // Use the modern Clipboard API with a fallback for older browsers.
    if (navigator.clipboard) {
      navigator.clipboard.writeText(userData.email).then(() => {
        setEmailCopied(true);
        setTimeout(() => setEmailCopied(false), 2000);
      }).catch(err => {
        console.error('Failed to copy email with Clipboard API:', err);
      });
    } else {
      // Fallback for non-secure contexts or older browsers
      try {
        const textArea = document.createElement('textarea');
        textArea.value = userData.email;
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        document.execCommand('copy');
        setEmailCopied(true);
        setTimeout(() => setEmailCopied(false), 2000);
        document.body.removeChild(textArea);
      } catch (err) {
        console.error('Failed to copy email using execCommand:', err);
      }
    }
  };

  return (
    <motion.section
      className={`pt-12 md:pt-24 mb-50 md:mb-60 ${className || ''}`}
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      aria-labelledby="hero-heading"
    >
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
        {/* Left Column - Content */}
        <div className="space-y-8 lg:space-y-12 order-1 lg:order-1">
          <div className="space-y-8">
            <motion.h1
              id="hero-heading"
              className="text-display text-4xl md:text-5xl lg:text-[3.5rem] xl:text-6xl font-bold text-foreground leading-tight"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              I build web
              <span className="block">
                apps{' '}
                <span className="relative font-apparel font-black italic -skew-x-12 inline-block bg-gradient-to-r from-purple-500 via-pink-500 via-orange-500 via-blue-500 to-purple-500 bg-[length:400%_400%] bg-clip-text text-transparent animate-gradient-shift [filter:drop-shadow(0_0_15px_rgba(148,56,213,0.5))_drop-shadow(0_0_1.5px_rgba(0,0,0,0.2))]">
                  that work
                </span>
              </span>
            </motion.h1>

            <motion.p
              className="text-body text-lg md:text-xl text-muted-foreground leading-relaxed max-w-2xl"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              {userData.bios.medium}
            </motion.p>
          </div>

          <motion.div
            className="space-y-6"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
          >
            <div className="flex flex-col sm:flex-row gap-6 items-center">
              {/* Primary CTA - This button works correctly */}
              <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                <Button
                  onClick={() => handleNavigation('/contact')}
                  className="group relative bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-300 ease-in-out px-8 py-4 text-lg font-semibold border-0 rounded-xl overflow-hidden"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/10 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700 ease-in-out" />
                  <span className="relative z-10">Let's Connect</span>
                </Button>
              </motion.div>

              {/* FIXED: Replaced the custom Button with a standard HTML <button> to isolate and fix the issue. */}
              <button
                type="button"
                onClick={handleCopyEmail}
                aria-label="Copy email address to clipboard"
                // The exact same classes are used to maintain the design.
                className="group relative px-6 py-4 inline-flex items-center justify-center text-sm font-medium text-muted-foreground hover:text-foreground hover:bg-muted/50 focus:text-foreground focus:bg-muted/50 focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2 focus:ring-offset-background rounded-xl transition-all duration-200 border border-transparent hover:border-border/50"
              >
                <span className="flex items-center gap-2">
                  {emailCopied ? (
                    <>
                      <Check className="w-4 h-4 text-green-500" />
                      Copied!
                    </>
                  ) : (
                    <>
                      <Copy className="w-4 h-4" />
                      {userData.email}
                    </>
                  )}
                </span>
              </button>
            </div>
          </motion.div>
        </div>

        {/* Right Column - Interactive Terminal */}
        <div className="order-2 lg:order-2 mt-8 lg:mt-0">
          <InteractiveTerminal />
        </div>
      </div>
    </motion.section>
  );
}
