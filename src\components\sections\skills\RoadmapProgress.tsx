import React from 'react'
import { motion } from 'framer-motion'
import { Trophy, Target, Clock, CheckCircle } from 'lucide-react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { roadmapSections } from './data'
import { RoadmapNode, SkillStatus } from './types'

interface RoadmapProgressProps {
  className?: string
}

export default function RoadmapProgress({ className }: RoadmapProgressProps) {
  // Get all nodes from all sections
  const allNodes = roadmapSections.flatMap(section => section.nodes)
  
  // Calculate progress statistics
  const totalNodes = allNodes.length
  const completedNodes = allNodes.filter(node => node.status === 'completed').length
  const inProgressNodes = allNodes.filter(node => node.status === 'in-progress').length
  const availableNodes = allNodes.filter(node => node.status === 'available').length
  const lockedNodes = allNodes.filter(node => node.status === 'locked').length
  
  const overallProgress = Math.round((completedNodes / totalNodes) * 100)
  const activeProgress = Math.round(((completedNodes + inProgressNodes * 0.5) / totalNodes) * 100)
  
  // Calculate average skill level
  const averageLevel = Math.round(
    allNodes.reduce((sum, node) => sum + node.level, 0) / totalNodes
  )
  
  // Calculate total experience
  const totalProjects = allNodes.reduce((sum, node) => sum + node.projects, 0)
  
  // Get section progress
  const sectionProgress = roadmapSections.map(section => {
    const sectionNodes = section.nodes
    const sectionCompleted = sectionNodes.filter(node => node.status === 'completed').length
    const sectionTotal = sectionNodes.length
    const sectionProgress = Math.round((sectionCompleted / sectionTotal) * 100)
    
    return {
      ...section,
      completed: sectionCompleted,
      total: sectionTotal,
      progress: sectionProgress
    }
  })
  
  // Get recent achievements (completed skills)
  const recentAchievements = allNodes
    .filter(node => node.status === 'completed')
    .sort((a, b) => b.level - a.level)
    .slice(0, 5)

  return (
    <div className={cn('space-y-6', className)}>
      {/* Overall Progress */}
      <Card className="bg-slate-800/50 border-slate-600 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <Target className="w-5 h-5 text-blue-400" />
            Overall Progress
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">{completedNodes}</div>
              <div className="text-sm text-slate-400">Completed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-400">{inProgressNodes}</div>
              <div className="text-sm text-slate-400">In Progress</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-400">{availableNodes}</div>
              <div className="text-sm text-slate-400">Available</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-400">{lockedNodes}</div>
              <div className="text-sm text-slate-400">Locked</div>
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-slate-300">Completion Rate</span>
              <span className="text-white font-medium">{overallProgress}%</span>
            </div>
            <Progress value={overallProgress} className="h-3" />
          </div>
          
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-slate-300">Active Progress</span>
              <span className="text-white font-medium">{activeProgress}%</span>
            </div>
            <Progress value={activeProgress} className="h-2" />
          </div>
        </CardContent>
      </Card>

      {/* Skills Overview */}
      <Card className="bg-slate-800/50 border-slate-600 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <Trophy className="w-5 h-5 text-yellow-400" />
            Skills Overview
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 rounded-lg bg-slate-700/30">
              <div className="text-2xl font-bold text-blue-400">{averageLevel}%</div>
              <div className="text-sm text-slate-400">Average Skill Level</div>
            </div>
            <div className="text-center p-4 rounded-lg bg-slate-700/30">
              <div className="text-2xl font-bold text-green-400">{totalProjects}</div>
              <div className="text-sm text-slate-400">Total Projects</div>
            </div>
            <div className="text-center p-4 rounded-lg bg-slate-700/30">
              <div className="text-2xl font-bold text-purple-400">{totalNodes}</div>
              <div className="text-sm text-slate-400">Skills Tracked</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Section Progress */}
      <Card className="bg-slate-800/50 border-slate-600 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <CheckCircle className="w-5 h-5 text-green-400" />
            Section Progress
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {sectionProgress.map((section, index) => (
            <motion.div
              key={section.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className="space-y-2"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div 
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: section.color }}
                  />
                  <span className="text-slate-300 font-medium">{section.title}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary" className="text-xs">
                    {section.completed}/{section.total}
                  </Badge>
                  <span className="text-sm text-white font-medium">
                    {section.progress}%
                  </span>
                </div>
              </div>
              <Progress value={section.progress} className="h-2" />
            </motion.div>
          ))}
        </CardContent>
      </Card>

      {/* Recent Achievements */}
      <Card className="bg-slate-800/50 border-slate-600 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <Trophy className="w-5 h-5 text-yellow-400" />
            Top Achievements
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {recentAchievements.map((achievement, index) => (
            <motion.div
              key={achievement.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="flex items-center justify-between p-3 rounded-lg bg-slate-700/30 hover:bg-slate-700/50 transition-colors"
            >
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 rounded-lg bg-slate-600/50 flex items-center justify-center">
                  <Trophy className="w-4 h-4 text-yellow-400" />
                </div>
                <div>
                  <div className="text-sm font-medium text-white">{achievement.name}</div>
                  <div className="text-xs text-slate-400">{achievement.experience}</div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="text-xs">
                  {achievement.projects} projects
                </Badge>
                <Badge 
                  variant="outline" 
                  className="text-xs bg-green-500/20 text-green-300 border-green-500/30"
                >
                  {achievement.level}%
                </Badge>
              </div>
            </motion.div>
          ))}
        </CardContent>
      </Card>
    </div>
  )
}
