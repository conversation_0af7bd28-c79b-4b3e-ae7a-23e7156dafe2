import React, { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ZoomIn, ZoomOut, RotateCcw, Filter, BarChart3 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import { roadmapSections, roadmapConnections } from './data'
import { RoadmapNode, RoadmapSection, SkillStatus } from './types'
import SkillNodeComponent from './SkillNodeComponent'
import SkillDetailModal from './SkillDetailModal'
import RoadmapProgress from './RoadmapProgress'

interface SkillsRoadmapProps {
  className?: string
}

export default function SkillsRoadmap({ className }: SkillsRoadmapProps) {
  const [selectedNode, setSelectedNode] = useState<RoadmapNode | null>(null)
  const [hoveredNode, setHoveredNode] = useState<string | null>(null)
  const [activeSection, setActiveSection] = useState<string>('all')
  const [showProgress, setShowProgress] = useState(false)
  const [zoom, setZoom] = useState(1)
  const [pan, setPan] = useState({ x: 0, y: 0 })
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })
  
  const svgRef = useRef<SVGSVGElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // Get all nodes from all sections
  const allNodes = roadmapSections.flatMap(section => section.nodes)
  
  // Filter nodes based on active section
  const visibleNodes = activeSection === 'all' 
    ? allNodes 
    : roadmapSections.find(s => s.id === activeSection)?.nodes || []

  // Get visible connections
  const visibleConnections = roadmapConnections.filter(conn => 
    visibleNodes.some(n => n.id === conn.from) && 
    visibleNodes.some(n => n.id === conn.to)
  )

  // Handle zoom
  const handleZoom = (delta: number) => {
    setZoom(prev => Math.max(0.5, Math.min(2, prev + delta)))
  }

  // Handle pan
  const handleMouseDown = (e: React.MouseEvent) => {
    const target = e.target as Element
    // Allow dragging on background elements only
    if (target.tagName === 'svg' ||
        (target.tagName === 'rect' && target.getAttribute('fill') === 'url(#grid)') ||
        target.classList.contains('roadmap-background')) {
      setIsDragging(true)
      setDragStart({ x: e.clientX - pan.x, y: e.clientY - pan.y })
      e.preventDefault()
    }
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging) {
      e.preventDefault()
      setPan({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y
      })
    }
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  // Reset view - center the content
  const resetView = () => {
    setZoom(1)
    // Center the roadmap content
    const containerWidth = containerRef.current?.clientWidth || 1200
    const containerHeight = containerRef.current?.clientHeight || 800
    setPan({
      x: (containerWidth - 800) / 2, // Center horizontally
      y: 50 // Small top margin
    })
  }

  // Initialize centered view
  useEffect(() => {
    resetView()
  }, [])

  // Get node by id
  const getNodeById = (id: string) => allNodes.find(n => n.id === id)

  // Get connection path
  const getConnectionPath = (from: RoadmapNode, to: RoadmapNode) => {
    const startX = from.position.x + 60 // Node width/2
    const startY = from.position.y + 30 // Node height/2
    const endX = to.position.x + 60
    const endY = to.position.y + 30
    
    // Create curved path
    const midX = (startX + endX) / 2
    const midY = (startY + endY) / 2
    const controlOffset = 50
    
    return `M ${startX} ${startY} Q ${midX} ${midY - controlOffset} ${endX} ${endY}`
  }

  // Get status color
  const getStatusColor = (status: SkillStatus) => {
    switch (status) {
      case 'completed': return '#10B981'
      case 'in-progress': return '#F59E0B'
      case 'available': return '#6B7280'
      case 'locked': return '#374151'
      default: return '#6B7280'
    }
  }

  return (
    <div className={cn('relative w-full h-screen', className)}>
      {/* Header */}
      <div className="absolute top-0 left-0 right-0 z-20 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold text-white mb-2">
              Full-Stack Developer Roadmap
            </h2>
            <p className="text-slate-300">
              Interactive journey through modern web development
            </p>
          </div>

          {/* Controls */}
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleZoom(0.1)}
              className="bg-slate-800/50 border-slate-600 text-white hover:bg-slate-700"
            >
              <ZoomIn className="w-4 h-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleZoom(-0.1)}
              className="bg-slate-800/50 border-slate-600 text-white hover:bg-slate-700"
            >
              <ZoomOut className="w-4 h-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={resetView}
              className="bg-slate-800/50 border-slate-600 text-white hover:bg-slate-700"
            >
              <RotateCcw className="w-4 h-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowProgress(!showProgress)}
              className="bg-slate-800/50 border-slate-600 text-white hover:bg-slate-700"
            >
              <BarChart3 className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Section Filter - Compact */}
      <div className="absolute top-24 left-6 z-20">
        <div className="flex flex-wrap gap-2">
          <Button
            variant={activeSection === 'all' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setActiveSection('all')}
            className="bg-slate-800/50 border-slate-600 text-white hover:bg-slate-700 text-xs px-3 py-1"
          >
            All
          </Button>
          {roadmapSections.map(section => (
            <Button
              key={section.id}
              variant={activeSection === section.id ? 'default' : 'outline'}
              size="sm"
              onClick={() => setActiveSection(section.id)}
              className="bg-slate-800/50 border-slate-600 text-white hover:bg-slate-700 text-xs px-3 py-1"
            >
              {section.id === 'fundamentals' ? 'Basics' :
               section.id === 'frontend-frameworks' ? 'Frontend' :
               section.id === 'styling-tools' ? 'Styling' :
               section.id === 'backend-development' ? 'Backend' :
               section.id === 'database-tools' ? 'Database' :
               section.id === 'testing-deployment' ? 'Deploy' :
               section.title}
            </Button>
          ))}
        </div>
      </div>

      {/* Legend - Compact */}
      <div className="absolute bottom-6 right-6 z-20">
        <div className="flex items-center gap-4 bg-slate-800/80 backdrop-blur-sm rounded-lg px-4 py-2 border border-slate-600">
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 rounded-full bg-green-500"></div>
            <span className="text-xs text-slate-300">Done</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
            <span className="text-xs text-slate-300">Active</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 rounded-full bg-blue-500"></div>
            <span className="text-xs text-slate-300">Ready</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 rounded-full bg-gray-500"></div>
            <span className="text-xs text-slate-300">Locked</span>
          </div>
        </div>
      </div>

      {/* Main SVG Canvas */}
      <div 
        ref={containerRef}
        className="absolute inset-0 overflow-hidden cursor-grab active:cursor-grabbing"
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      >
        <svg
          ref={svgRef}
          className="w-full h-full"
          style={{
            transform: `translate(${pan.x}px, ${pan.y}px) scale(${zoom})`,
            transformOrigin: 'center center'
          }}
        >
          {/* Background Grid */}
          <defs>
            <pattern
              id="grid"
              width="50"
              height="50"
              patternUnits="userSpaceOnUse"
            >
              <path
                d="M 50 0 L 0 0 0 50"
                fill="none"
                stroke="rgba(148, 163, 184, 0.1)"
                strokeWidth="1"
              />
            </pattern>
          </defs>
          <rect
            width="100%"
            height="100%"
            fill="url(#grid)"
            className="roadmap-background pointer-events-auto"
          />

          {/* Connections */}
          {visibleConnections.map((connection, index) => {
            const fromNode = getNodeById(connection.from)
            const toNode = getNodeById(connection.to)

            if (!fromNode || !toNode) return null

            return (
              <motion.path
                key={`${connection.from}-${connection.to}`}
                d={getConnectionPath(fromNode, toNode)}
                fill="none"
                stroke={connection.type === 'prerequisite' ? '#10B981' : '#6B7280'}
                strokeWidth={connection.type === 'prerequisite' ? 2 : 1}
                strokeDasharray={connection.type === 'optional' ? '5,5' : 'none'}
                opacity={0.6}
                className="transition-opacity duration-300"
                initial={{ pathLength: 0, opacity: 0 }}
                animate={{ pathLength: 1, opacity: 0.6 }}
                transition={{
                  duration: 1,
                  delay: index * 0.1,
                  ease: "easeInOut"
                }}
                whileHover={{
                  opacity: 1,
                  strokeWidth: connection.type === 'prerequisite' ? 3 : 2
                }}
              />
            )
          })}

          {/* Skill Nodes */}
          {visibleNodes.map(node => (
            <SkillNodeComponent
              key={node.id}
              node={node}
              isHovered={hoveredNode === node.id}
              onHover={setHoveredNode}
              onClick={setSelectedNode}
            />
          ))}
        </svg>
      </div>

      {/* Progress Panel */}
      <AnimatePresence>
        {showProgress && (
          <motion.div
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ type: 'spring', damping: 20 }}
            className="absolute top-0 right-0 w-96 h-full bg-slate-900/95 backdrop-blur-lg border-l border-slate-700 overflow-y-auto z-30"
          >
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-white">Progress Overview</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowProgress(false)}
                  className="text-slate-400 hover:text-white"
                >
                  ×
                </Button>
              </div>
              <RoadmapProgress />
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Skill Detail Modal */}
      <AnimatePresence>
        {selectedNode && (
          <SkillDetailModal
            node={selectedNode}
            onClose={() => setSelectedNode(null)}
          />
        )}
      </AnimatePresence>
    </div>
  )
}
