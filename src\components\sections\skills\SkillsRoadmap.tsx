import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import { roadmapSections } from './data'
import { RoadmapNode, SkillStatus } from './types'
import SkillDetailModal from './SkillDetailModal'

interface SkillsRoadmapProps {
  className?: string
}

export default function SkillsRoadmap({ className }: SkillsRoadmapProps) {
  const [selectedNode, setSelectedNode] = useState<RoadmapNode | null>(null)
  const [activeSection, setActiveSection] = useState<string>('all')

  // Filter sections based on active section
  const visibleSections = activeSection === 'all'
    ? roadmapSections
    : roadmapSections.filter(section => section.id === activeSection)

  // Get status color
  const getStatusColor = (status: SkillStatus) => {
    switch (status) {
      case 'completed': return 'bg-green-500'
      case 'in-progress': return 'bg-yellow-500'
      case 'available': return 'bg-blue-500'
      case 'locked': return 'bg-gray-500'
      default: return 'bg-gray-500'
    }
  }


  return (
    <div className={cn('w-full py-16', className)}>
      {/* Header */}
      <div className="text-center mb-12">
        <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
          Skills Roadmap
        </h2>
        <p className="text-slate-400 text-lg max-w-2xl mx-auto mb-8">
          My journey through modern web development technologies
        </p>

        {/* Section Filter */}
        <div className="flex flex-wrap justify-center gap-2 mb-8">
          <Button
            variant={activeSection === 'all' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setActiveSection('all')}
            className="text-xs"
          >
            All
          </Button>
          {roadmapSections.map(section => (
            <Button
              key={section.id}
              variant={activeSection === section.id ? 'default' : 'outline'}
              size="sm"
              onClick={() => setActiveSection(section.id)}
              className="text-xs"
            >
              {section.title}
            </Button>
          ))}
        </div>
      </div>

      {/* Skills Grid */}
      <div className="max-w-7xl mx-auto px-6">
        {visibleSections.map((section, sectionIndex) => (
          <motion.div
            key={section.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: sectionIndex * 0.1 }}
            className="mb-16"
          >
            <h3 className="text-2xl font-bold text-white mb-6 text-center">
              {section.title}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {section.nodes.map((node, nodeIndex) => (
                <motion.div
                  key={node.id}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: (sectionIndex * 0.1) + (nodeIndex * 0.05) }}
                  whileHover={{ scale: 1.05 }}
                  className="cursor-pointer"
                  onClick={() => setSelectedNode(node)}
                >
                  <Card className="bg-slate-800/50 border-slate-600 hover:border-slate-500 transition-all duration-200">
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3 mb-3">
                        <div className="text-2xl">{node.icon}</div>
                        <div className="flex-1">
                          <h4 className="font-semibold text-white text-sm">{node.name}</h4>
                          <p className="text-xs text-slate-400">{node.experience}</p>
                        </div>
                        <div className={cn("w-3 h-3 rounded-full", getStatusColor(node.status))} />
                      </div>

                      {/* Progress Bar */}
                      <div className="w-full bg-slate-700 rounded-full h-2 mb-2">
                        <div
                          className={cn("h-2 rounded-full transition-all duration-300", getStatusColor(node.status))}
                          style={{ width: `${node.proficiency}%` }}
                        />
                      </div>

                      <div className="flex items-center justify-between text-xs">
                        <span className="text-slate-400">{node.proficiency}%</span>
                        <Badge variant="outline" className="text-xs">
                          {node.difficulty}
                        </Badge>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.div>
        ))}
      </div>


      {/* Legend */}
      <div className="flex justify-center mt-12">
        <div className="flex items-center gap-6 bg-slate-800/50 backdrop-blur-sm rounded-lg px-6 py-3 border border-slate-600">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-green-500"></div>
            <span className="text-sm text-slate-300">Completed</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
            <span className="text-sm text-slate-300">In Progress</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-blue-500"></div>
            <span className="text-sm text-slate-300">Available</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-gray-500"></div>
            <span className="text-sm text-slate-300">Locked</span>
          </div>
        </div>
      </div>


      {/* Skill Detail Modal */}
      <SkillDetailModal
        node={selectedNode}
        isOpen={!!selectedNode}
        onClose={() => setSelectedNode(null)}
      />
    </div>
  )
}
