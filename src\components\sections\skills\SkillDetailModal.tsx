import React from 'react'
import { motion } from 'framer-motion'
import { X, ExternalLink, BookOpen, Code, FileText, Clock, Target, Zap } from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { cn } from '@/lib/utils'
import { RoadmapNode } from './types'
import { renderIcon } from './utils'

interface SkillDetailModalProps {
  node: RoadmapNode
  onClose: () => void
}

export default function SkillDetailModal({ node, onClose }: SkillDetailModalProps) {
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner': return 'bg-green-500/20 text-green-300 border-green-500/30'
      case 'Intermediate': return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30'
      case 'Advanced': return 'bg-orange-500/20 text-orange-300 border-orange-500/30'
      case 'Expert': return 'bg-red-500/20 text-red-300 border-red-500/30'
      default: return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-500/20 text-green-300 border-green-500/30'
      case 'in-progress': return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30'
      case 'available': return 'bg-blue-500/20 text-blue-300 border-blue-500/30'
      case 'locked': return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
      default: return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        transition={{ type: "spring", duration: 0.3 }}
        className="relative w-full max-w-4xl max-h-[90vh] overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        <Card className="bg-slate-900/95 border-slate-700 backdrop-blur-lg shadow-2xl">
          {/* Header */}
          <CardHeader className="pb-4">
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-4">
                <div className="w-16 h-16 rounded-xl bg-slate-800/60 flex items-center justify-center">
                  {renderIcon(node.icon, "w-8 h-8")}
                </div>
                <div>
                  <CardTitle className="text-2xl font-bold text-white mb-2">
                    {node.name}
                  </CardTitle>
                  <div className="flex items-center gap-3">
                    <Badge
                      variant="outline"
                      className={cn("text-sm", getStatusColor(node.status))}
                    >
                      {node.status.charAt(0).toUpperCase() + node.status.slice(1)}
                    </Badge>
                    <Badge
                      variant="outline"
                      className={cn("text-sm", getDifficultyColor(node.difficulty))}
                    >
                      {node.difficulty}
                    </Badge>
                    <Badge variant="secondary" className="text-sm">
                      {node.category}
                    </Badge>
                  </div>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="text-slate-400 hover:text-white"
              >
                <X className="w-5 h-5" />
              </Button>
            </div>
          </CardHeader>

          <CardContent className="space-y-6 max-h-[60vh] overflow-y-auto">
            {/* Progress and Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card className="bg-slate-800/50 border-slate-700">
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Target className="w-4 h-4 text-blue-400" />
                    <span className="text-sm font-medium text-slate-300">Proficiency</span>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-slate-400">Level</span>
                      <span className="text-white font-medium">{node.level}%</span>
                    </div>
                    <Progress value={node.level} className="h-2" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-slate-800/50 border-slate-700">
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Code className="w-4 h-4 text-green-400" />
                    <span className="text-sm font-medium text-slate-300">Experience</span>
                  </div>
                  <div className="space-y-1">
                    <div className="text-lg font-bold text-white">{node.experience}</div>
                    <div className="text-sm text-slate-400">{node.projects} projects</div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-slate-800/50 border-slate-700">
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Clock className="w-4 h-4 text-yellow-400" />
                    <span className="text-sm font-medium text-slate-300">Time to Learn</span>
                  </div>
                  <div className="text-lg font-bold text-white">{node.estimatedTime}</div>
                </CardContent>
              </Card>
            </div>

            {/* Description */}
            <div>
              <h3 className="text-lg font-semibold text-white mb-3">About This Skill</h3>
              <p className="text-slate-300 leading-relaxed">{node.description}</p>
            </div>

            {/* Key Topics */}
            <div>
              <h3 className="text-lg font-semibold text-white mb-3">Key Topics</h3>
              <div className="flex flex-wrap gap-2">
                {node.keyTopics.map((topic, index) => (
                  <Badge
                    key={index}
                    variant="secondary"
                    className="bg-slate-700/50 text-slate-300 border-slate-600"
                  >
                    {topic}
                  </Badge>
                ))}
              </div>
            </div>

            <Separator className="bg-slate-700" />

            {/* Learning Resources */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Documentation */}
              <div>
                <div className="flex items-center gap-2 mb-3">
                  <FileText className="w-5 h-5 text-blue-400" />
                  <h4 className="font-semibold text-white">Documentation</h4>
                </div>
                <div className="space-y-2">
                  {node.resources.documentation.map((doc, index) => (
                    <div
                      key={index}
                      className="flex items-center gap-2 p-2 rounded-lg bg-slate-800/30 hover:bg-slate-800/50 transition-colors cursor-pointer"
                    >
                      <ExternalLink className="w-4 h-4 text-slate-400" />
                      <span className="text-sm text-slate-300">{doc}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Courses */}
              <div>
                <div className="flex items-center gap-2 mb-3">
                  <BookOpen className="w-5 h-5 text-green-400" />
                  <h4 className="font-semibold text-white">Courses</h4>
                </div>
                <div className="space-y-2">
                  {node.resources.courses.map((course, index) => (
                    <div
                      key={index}
                      className="flex items-center gap-2 p-2 rounded-lg bg-slate-800/30 hover:bg-slate-800/50 transition-colors cursor-pointer"
                    >
                      <ExternalLink className="w-4 h-4 text-slate-400" />
                      <span className="text-sm text-slate-300">{course}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Projects */}
              <div>
                <div className="flex items-center gap-2 mb-3">
                  <Zap className="w-5 h-5 text-yellow-400" />
                  <h4 className="font-semibold text-white">Practice Projects</h4>
                </div>
                <div className="space-y-2">
                  {node.resources.projects.map((project, index) => (
                    <div
                      key={index}
                      className="flex items-center gap-2 p-2 rounded-lg bg-slate-800/30 hover:bg-slate-800/50 transition-colors cursor-pointer"
                    >
                      <Code className="w-4 h-4 text-slate-400" />
                      <span className="text-sm text-slate-300">{project}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Articles */}
              <div>
                <div className="flex items-center gap-2 mb-3">
                  <FileText className="w-5 h-5 text-purple-400" />
                  <h4 className="font-semibold text-white">Articles & Guides</h4>
                </div>
                <div className="space-y-2">
                  {node.resources.articles.map((article, index) => (
                    <div
                      key={index}
                      className="flex items-center gap-2 p-2 rounded-lg bg-slate-800/30 hover:bg-slate-800/50 transition-colors cursor-pointer"
                    >
                      <ExternalLink className="w-4 h-4 text-slate-400" />
                      <span className="text-sm text-slate-300">{article}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Prerequisites and Unlocks */}
            {(node.prerequisites.length > 0 || node.unlocks.length > 0) && (
              <>
                <Separator className="bg-slate-700" />
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {node.prerequisites.length > 0 && (
                    <div>
                      <h4 className="font-semibold text-white mb-3">Prerequisites</h4>
                      <div className="space-y-2">
                        {node.prerequisites.map((prereq, index) => (
                          <Badge
                            key={index}
                            variant="outline"
                            className="bg-red-500/20 text-red-300 border-red-500/30"
                          >
                            {prereq}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {node.unlocks.length > 0 && (
                    <div>
                      <h4 className="font-semibold text-white mb-3">Unlocks</h4>
                      <div className="space-y-2">
                        {node.unlocks.map((unlock, index) => (
                          <Badge
                            key={index}
                            variant="outline"
                            className="bg-green-500/20 text-green-300 border-green-500/30"
                          >
                            {unlock}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  )
}
