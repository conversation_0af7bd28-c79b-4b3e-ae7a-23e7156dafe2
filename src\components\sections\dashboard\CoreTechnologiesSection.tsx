import { motion } from 'framer-motion'
import { useNavigate } from 'react-router-dom'
import { scrollToTopInstant } from '@/components/ScrollToTop'
import { Button } from '@/components/ui/button'
import { BaseSectionProps } from './types'
import { technicalSkills } from '@/data/skills/skillsData'

export default function CoreTechnologiesSection({ className }: BaseSectionProps) {
  const navigate = useNavigate()

  const handleNavigation = (path: string) => {
    scrollToTopInstant()
    navigate(path)
  }

  // Create multiple sets of technologies for seamless looping
  const allTechnologies = [...technicalSkills, ...technicalSkills, ...technicalSkills]

  return (
    <section className={`py-20 lg:py-32 ${className || ''}`}>
      {/* Header Section - Matching FeaturedWorkSection */}
      <div className="container mx-auto px-4 text-center mb-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="mb-2"
        >
          <span className="text-caption font-medium text-muted-foreground tracking-wider uppercase">
            CORE TECHNOLOGIES
          </span>
        </motion.div>
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight mb-6"
        >
          Core{' '}
          <span className="relative font-apparel font-black italic -skew-x-12 inline-block bg-gradient-to-r from-purple-500 via-pink-500 via-orange-500 via-blue-500 to-purple-500 bg-[length:400%_400%] bg-clip-text text-transparent animate-gradient-shift [filter:drop-shadow(0_0_15px_rgba(148,56,213,0.5))_drop-shadow(0_0_1.5px_rgba(0,0,0,0.2))]">
            technologies
          </span>
        </motion.h2>
      </div>

      {/* Enhanced Infinite scrolling technology icons */}
      <div className="relative overflow-hidden py-12 mb-16">
        <motion.div
          className="flex gap-12 items-center"
          animate={{
            x: [0, -120 * technicalSkills.length]
          }}
          transition={{
            x: {
              repeat: Infinity,
              repeatType: "loop",
              duration: technicalSkills.length * 2.5,
              ease: "linear",
            },
          }}
          style={{ width: `${technicalSkills.length * 240}px` }}
        >
          {allTechnologies.map((skill, index) => {
            const IconComponent = skill.icon
            return (
              <motion.div
                key={`${skill.name}-${index}`}
                className="flex flex-col items-center space-y-3 min-w-[140px] group"
                whileHover={{ scale: 1.08, y: -4 }}
                transition={{ duration: 0.3, ease: "easeOut" }}
              >
                <div className="w-16 h-16 flex items-center justify-center rounded-2xl bg-gradient-to-br from-background/80 to-background/40 border border-border/30 group-hover:border-primary/40 group-hover:shadow-lg group-hover:shadow-primary/10 transition-all duration-300 backdrop-blur-sm">
                  <IconComponent
                    className="w-8 h-8 transition-transform duration-300 group-hover:scale-110"
                    style={{ color: skill.color }}
                  />
                </div>
                <p className="text-sm font-semibold text-muted-foreground group-hover:text-foreground transition-colors duration-300 text-center">
                  {skill.name}
                </p>
              </motion.div>
            )
          })}
        </motion.div>

        {/* Enhanced gradient overlays */}
        <div className="absolute left-0 top-0 bottom-0 w-32 bg-gradient-to-r from-background via-background/80 to-transparent pointer-events-none z-10" />
        <div className="absolute right-0 top-0 bottom-0 w-32 bg-gradient-to-l from-background via-background/80 to-transparent pointer-events-none z-10" />
      </div>

      {/* Clean CTA Section */}
      <div className="container mx-auto px-4 text-center">
        <Button
          onClick={() => handleNavigation('/skills')}
          className="group relative bg-neutral-900/50 hover:bg-neutral-800/50 text-white shadow-[inset_0_0_20px_0_rgba(255,255,255,0.08)] hover:shadow-[inset_0_0_25px_0_rgba(255,255,255,0.12)] transition-all duration-300 ease-in-out px-10 py-4 text-lg font-medium border border-neutral-700/50 hover:border-neutral-600/50 rounded-xl backdrop-blur-lg"
        >
          <span className="relative z-10">Explore my full skillset</span>
        </Button>
      </div>
    </section>
  )
}
