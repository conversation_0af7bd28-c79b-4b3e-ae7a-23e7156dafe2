import { Download, Mail } from "lucide-react"
import { <PERSON>Linkedin, SiGith<PERSON> } from "react-icons/si"
import { userData } from "@/data/personal/userData"

// It's good practice to keep asset paths as constants
const PROFILE_IMAGE_URL = "/image/profile.png"

export function Sidebar() {
  return (
    // The main fixed container for the sidebar. No changes needed here.
    <div className="fixed left-0 top-0 z-30 h-screen w-80 border-r border-border/50 bg-background/80 backdrop-blur-xl supports-[backdrop-filter]:bg-background/60 hidden lg:block">
      {/* Background gradient overlay. No changes needed. */}
      <div className="absolute inset-0 bg-gradient-to-b from-background/90 via-background/95 to-background opacity-60"></div>
     
      {/* This div is the scrollable container. It's crucial for handling overflow. */}
      <div className="relative h-full overflow-y-auto scrollbar-sidebar">
       
        {/* KEY CHANGE: The main flex container.
          - We remove `justify-between` to allow content to flow naturally from top to bottom.
          - This is essential for scrolling to work correctly when content overflows.
          - Added pb-safe for consistent bottom spacing
        */}
        <div className="flex h-full min-h-screen flex-col p-10 pb-safe">
         
          {/* Header Section Wrapper.
            - We add `flex-grow` here. This is the magic ingredient.
            - It tells this div to expand and take up all available vertical space,
              which pushes the footer down to the bottom of the screen when there's room.
            - When zoomed in, there's no extra space, so it behaves like a normal block.
          */}
          <div className="flex-grow space-y-10">
           
            {/* Avatar Section */}
            <div className="flex justify-center">
              <div className="relative group">
                <div className="absolute -inset-0.5 bg-gradient-to-br from-primary/30 to-primary/10 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-all duration-500"></div>
                <div className="relative">
                  <img
                    src={PROFILE_IMAGE_URL}
                    alt="CJ Jutba"
                    className="w-32 h-32 rounded-full object-cover shadow-xl border-2 border-background/50 group-hover:shadow-2xl transition-all duration-300"
                  />
                  <div className="absolute bottom-1 right-1 bg-emerald-500 w-6 h-6 rounded-full border-2 border-background shadow-lg">
                    <div className="w-full h-full bg-emerald-400 rounded-full animate-pulse opacity-75"></div>
                  </div>
                </div>
              </div>
            </div>

            {/* Name & Title */}
            <div className="text-center space-y-4">
              <h1 className="text-display text-5xl text-foreground">
                {userData.name}
              </h1>
              <div className="space-y-3">
                <p className="text-heading text-xl text-muted-foreground">{userData.title}</p>
                <div className="h-px w-16 bg-gradient-to-r from-transparent via-primary/60 to-transparent mx-auto"></div>
              </div>
            </div>

            {/* Headline */}
            <div className="text-center px-2">
              <p className="text-caption text-muted-foreground">
                {userData.bios.short}
              </p>
            </div>


          </div>

          {/* Footer Section Wrapper.
            - We use a semantic `<footer>` tag for better structure.
            - `mt-10` provides a minimum top margin, ensuring space even when compressed.
            - `pt-8` and `border-t` create a clean visual separation from the content above.
          */}
          <footer className="mt-auto pt-8 border-t border-border/30 space-y-8">
            {/* Social Links */}
            <div className="flex items-center justify-center gap-8">
              <a
                href={userData.social.linkedin}
                target="_blank"
                rel="noopener noreferrer"
                className="text-muted-foreground/70 hover:text-[#0077B5] hover:scale-110 transition-all duration-200"
                title="LinkedIn"
              >
                <SiLinkedin className="w-6 h-6" />
                <span className="sr-only">LinkedIn</span>
              </a>
              <a
                href={userData.social.github}
                target="_blank"
                rel="noopener noreferrer"
                className="text-muted-foreground/70 hover:text-foreground hover:scale-110 transition-all duration-200"
                title="GitHub"
              >
                <SiGithub className="w-6 h-6" />
                <span className="sr-only">GitHub</span>
              </a>
            </div>

            {/* Download Resume Link */}
            <div className="text-center">
              <a
                href="/resume.pdf"
                download
                className="inline-flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-all duration-300 group font-medium hover:underline underline-offset-4"
              >
                <Download className="w-4 h-4 group-hover:translate-y-0.5 transition-transform duration-200" />
                Download Resume
              </a>
            </div>
          </footer>
         
        </div>
      </div>
    </div>
  )
}