import { motion } from 'framer-motion'
import { useNavigate } from 'react-router-dom'
import { GraduationCap, Rocket } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { scrollToTopInstant } from '@/components/ScrollToTop'
import { BaseSectionProps } from './types'

export default function JourneySummarySection({ className }: BaseSectionProps) {
  const navigate = useNavigate()

  const handleNavigation = (path: string) => {
    scrollToTopInstant()
    navigate(path)
  }

  return (
    <section className={`py-20 lg:py-32 ${className || ''}`}>
      {/* Header Section - Matching FeaturedWorkSection */}
      <div className="container mx-auto px-4 text-center mb-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="mb-2"
        >
          <span className="text-caption font-medium text-muted-foreground tracking-wider uppercase">
            MY JOURNEY
          </span>
        </motion.div>
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight mb-6"
        >
          My{' '}
          <span className="relative font-apparel font-black italic -skew-x-12 inline-block bg-gradient-to-r from-purple-500 via-pink-500 via-orange-500 via-blue-500 to-purple-500 bg-[length:400%_400%] bg-clip-text text-transparent animate-gradient-shift [filter:drop-shadow(0_0_15px_rgba(148,56,213,0.5))_drop-shadow(0_0_1.5px_rgba(0,0,0,0.2))]">
            journey
          </span>
        </motion.h2>
      </div>

      {/* Enhanced Content */}
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="max-w-4xl mx-auto"
        >
          <div className="relative">
            <div className="bg-neutral-900/50 backdrop-blur-lg shadow-[inset_0_0_20px_0_rgba(255,255,255,0.08)] rounded-3xl p-12 transition-all duration-500">
              {/* Compact Timeline */}
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="flex items-center justify-center space-x-6 mb-12 max-w-md mx-auto"
              >
                <div className="flex items-center space-x-3 group">
                  <div className="w-12 h-12 bg-neutral-800/50 rounded-2xl flex items-center justify-center border border-neutral-700/50 group-hover:scale-110 transition-transform duration-300">
                    <GraduationCap className="w-6 h-6 text-purple-500" />
                  </div>
                  <div className="text-center">
                    <span className="text-lg font-bold text-purple-500">May 2025</span>
                    <p className="text-xs text-muted-foreground">Graduation</p>
                  </div>
                </div>

                <div className="w-16 h-[3px] bg-gradient-to-r from-purple-500 via-pink-500 to-primary rounded-full relative overflow-hidden">
                  <motion.div
                    initial={{ x: "-100%" }}
                    whileInView={{ x: "100%" }}
                    viewport={{ once: true }}
                    transition={{ duration: 2, delay: 0.6, ease: "easeInOut" }}
                    className="absolute inset-0 bg-gradient-to-r from-transparent via-white/50 to-transparent"
                  />
                </div>

                <div className="flex items-center space-x-3 group">
                  <div className="text-center">
                    <span className="text-lg font-bold text-primary">Present</span>
                    <p className="text-xs text-muted-foreground">Developer</p>
                  </div>
                  <div className="w-12 h-12 bg-neutral-800/50 rounded-2xl flex items-center justify-center border border-neutral-700/50 group-hover:scale-110 transition-transform duration-300">
                    <Rocket className="w-6 h-6 text-primary" />
                  </div>
                </div>
              </motion.div>

              {/* Story */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.6 }}
                className="text-center mb-12"
              >
                <p className="text-xl text-muted-foreground leading-relaxed max-w-3xl mx-auto">
                  After graduating with a Computer Engineering degree in May 2025, I discovered my passion for frontend
                  development. I'm committed to continuous learning and building applications that provide exceptional user experiences.
                </p>
              </motion.div>

              {/* Clean CTA */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.8 }}
                className="text-center"
              >
                <Button
                  onClick={() => handleNavigation('/about')}
                  className="group relative bg-neutral-900/50 hover:bg-neutral-800/50 text-white shadow-[inset_0_0_20px_0_rgba(255,255,255,0.08)] hover:shadow-[inset_0_0_25px_0_rgba(255,255,255,0.12)] transition-all duration-300 ease-in-out px-10 py-4 text-lg font-medium border border-neutral-700/50 hover:border-neutral-600/50 rounded-xl backdrop-blur-lg"
                >
                  <span className="relative z-10">See my full journey</span>
                </Button>
              </motion.div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
