import { useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useNavigate } from 'react-router-dom'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ArrowRight } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useScrollTracking } from '@/hooks/useScrollTracking'
import { featuredProjects, type Project } from '@/data/projects/projectsData'
import { scrollToTopInstant } from '@/components/ScrollToTop'
// React Icons
import {
  SiReact, SiNextdotjs, SiTypescript, SiTailwindcss, SiNodedotjs,
  SiPostgresql, SiPrisma, SiStripe, SiSocketdotio, SiVite,
  SiFramer, SiJavascript, SiExpress, SiMongodb, SiChartdotjs
} from 'react-icons/si'

interface FeaturedWorkSectionProps {
  className?: string
}

// Moved getProjectStyles to a higher scope to be shared
const getProjectStyles = (index: number) => {
  const palettes = [
    {
      gradient: 'from-[#240914] via-[#b0205b] to-[#f287b0]',
      glow: '[filter:drop-shadow(0_4px_20px_rgba(242,135,176,0.4))]',
      innerGlow: 'shadow-[inset_0_2px_5px_0_rgba(242,135,176,0.15)]',
      highlight: '#b0205b',
    },
    {
      gradient: 'from-[#080a25] via-[#2632ad] to-[#c0beea]',
      glow: '[filter:drop-shadow(0_4px_20px_rgba(192,190,234,0.4))]',
      innerGlow: 'shadow-[inset_0_2px_5px_0_rgba(192,190,234,0.15)]',
      highlight: '#2632ad',
    },
    {
      gradient: 'from-[#0f2f2d] via-[#2db7a8] to-[#c5ece3]',
      glow: '[filter:drop-shadow(0_4px_20px_rgba(197,236,227,0.4))]',
      innerGlow: 'shadow-[inset_0_2px_5px_0_rgba(197,236,227,0.15)]',
      highlight: '#2db7a8',
    },
  ];
  return palettes[index % palettes.length];
};


export default function FeaturedWorkSection({ className }: FeaturedWorkSectionProps) {
  const { activeIndex, registerElement } = useScrollTracking(featuredProjects.length, {
    threshold: 0.6,
    rootMargin: '-10% 0px -10% 0px'
  })

  const navigate = useNavigate()
  const activeProject = featuredProjects[activeIndex]
  const activeProjectStyles = getProjectStyles(activeIndex) // Get styles for the active project

  const handleNavigate = (path: string) => {
    scrollToTopInstant()
    navigate(path)
  }

  if (!activeProject) {
    return null
  }

  return (
    <section className={cn("mb-24 md:mb-32 bg-background", className)}>
      {/* Section Header */}
      <div className="mb-20 text-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="mb-2"
        >
          <span className="text-caption font-medium text-muted-foreground tracking-wider uppercase">
            CURATED CASE STUDIES
          </span>
        </motion.div>
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight mb-6"
        >
          Featured{' '}
          <span className="relative font-apparel font-black italic -skew-x-12 inline-block bg-gradient-to-r from-purple-500 via-pink-500 via-orange-500 via-blue-500 to-purple-500 bg-[length:400%_400%] bg-clip-text text-transparent animate-gradient-shift [filter:drop-shadow(0_0_15px_rgba(148,56,213,0.5))_drop-shadow(0_0_1.5px_rgba(0,0,0,0.2))]">
            project
          </span>
        </motion.h2>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-12 xl:gap-16">
        {/* Left Column (Project Cards) */}
        <div className="space-y-20 lg:space-y-24" id="projects-container">
          {featuredProjects.map((project, index) => (
            <ProjectCard
              key={project.id}
              project={project}
              index={index}
              isActive={index === activeIndex}
              onRegister={registerElement}
            />
          ))}
        </div>

        {/* Right Column (Project Details) */}
        <div className="lg:sticky lg:top-[33vh] lg:h-fit">
          <AnimatePresence mode="wait">
            <ProjectDetails
              key={activeProject.id}
              project={activeProject}
              highlightColor={activeProjectStyles.highlight}
            />
          </AnimatePresence>
        </div>
      </div>

      <div className="text-center mt-24 lg:mt-32">
        <Button
          onClick={() => handleNavigate('/projects')}
          className="group relative bg-neutral-900/50 hover:bg-neutral-800/50 text-white shadow-[inset_0_0_20px_0_rgba(255,255,255,0.08)] hover:shadow-[inset_0_0_25px_0_rgba(255,255,255,0.12)] transition-all duration-300 ease-in-out px-8 py-3 text-base font-medium border border-neutral-700/50 hover:border-neutral-600/50 rounded-xl backdrop-blur-lg"
        >
          <span className="relative z-10">See more projects</span>
        </Button>
      </div>
    </section>
  )
}

// Project Card Component
interface ProjectCardProps {
  project: Project
  index: number
  isActive: boolean
  onRegister: (index: number, element: HTMLElement | null) => void
}

function ProjectCard({ project, index, isActive, onRegister }: ProjectCardProps) {
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    onRegister(index, ref.current)
  }, [index, onRegister])

  const styles = getProjectStyles(index);

  return (
    <motion.div
      ref={ref}
      id={`project-${index}`}
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, margin: "-100px" }}
      transition={{ duration: 0.8, delay: index * 0.1 }}
      className="relative group"
    >
      <Card className={cn(
        "transition-all duration-700 shadow-lg rounded-3xl",
        "flex flex-col",
        "h-[450px]",
        "bg-gradient-to-b", styles.gradient,
        "relative ring-1 ring-inset ring-white/20",
        isActive ? "shadow-2xl scale-[1.02] lg:scale-105" : "hover:shadow-xl"
      )}>
        {/* Header Section */}
        <div className="px-8 pt-6 pb-4 flex-shrink-0 z-10">
          <div className="flex items-center gap-1.5 mb-6">
            <div className="w-3 h-3 rounded-full bg-white/25"></div>
            <div className="w-3 h-3 rounded-full bg-white/25"></div>
            <div className="w-3 h-3 rounded-full bg-white/25"></div>
          </div>
          <div className="flex justify-between items-start gap-4">
            <p className="text-white/90 text-base leading-relaxed">
              {project.description}
            </p>
            <ArrowRight className="w-5 h-5 text-white/70 flex-shrink-0 transition-transform group-hover:translate-x-1" />
          </div>
        </div>

        {/* Mockup section wrapper */}
        <div className="flex-1 relative">
            <div className={cn(
                "absolute top-0 bottom-0 left-6 right-6",
                "bg-neutral-950",
                "rounded-xl",
                "flex items-center justify-center",
                styles.innerGlow,
                isActive && styles.glow
            )}>
                <div className="text-center text-white/70">
                    <div className="w-20 h-20 mx-auto mb-4 bg-white/10 rounded-lg flex items-center justify-center">
                        <div className="w-10 h-10 bg-white/20 rounded-md"></div>
                    </div>
                    <p className="text-base font-medium">{project.title}</p>
                    <p className="text-sm text-white/50 mt-1">Preview Coming Soon</p>
                </div>
            </div>
        </div>
      </Card>
    </motion.div>
  )
}

function ProjectMockup({ project, styles, isActive }: { project: Project; styles: any; isActive: boolean }) {
  return (
    <div className={cn(
      "h-full flex flex-col bg-black/50 backdrop-blur-sm rounded-xl p-3 border transition-shadow duration-700",
      styles.border,
      isActive ? styles.glow : 'shadow-lg shadow-black/30'
    )}>
      <div className="flex-shrink-0 h-6 flex items-center px-2">
        <div className="flex items-center gap-1.5">
          <div className="w-2.5 h-2.5 rounded-full bg-neutral-700"></div>
          <div className="w-2.5 h-2.5 rounded-full bg-neutral-700"></div>
          <div className="w-2.5 h-2.5 rounded-full bg-neutral-700"></div>
        </div>
      </div>
      <div className={cn(
        "flex-1 bg-gradient-to-br from-neutral-900 to-black rounded-md flex items-center justify-center relative overflow-hidden mt-1",
        styles.innerGlow,
      )}>
        <div className="text-center text-white/70">
          <div className="w-20 h-20 mx-auto mb-4 bg-white/10 rounded-lg flex items-center justify-center">
            <div className="w-10 h-10 bg-white/20 rounded-md"></div>
          </div>
          <p className="text-base font-medium">{project.title}</p>
          <p className="text-sm text-white/50 mt-1">Preview Coming Soon</p>
        </div>
      </div>
    </div>
  );
}

// Project Details Component
interface ProjectDetailsProps {
  project: Project
  highlightColor: string;
}

const getTechIcon = (tech: string) => {
  // UPDATED: Icon size reduced from w-4 h-4 to w-3 h-3.
  const iconMap: Record<string, JSX.Element> = {
    'React': <SiReact className="w-3 h-3" style={{ color: '#61DAFB' }} />,
    'Next.js': <SiNextdotjs className="w-3 h-3" style={{ color: '#FFFFFF' }} />,
    'TypeScript': <SiTypescript className="w-3 h-3" style={{ color: '#3178C6' }} />,
    'Tailwind CSS': <SiTailwindcss className="w-3 h-3" style={{ color: '#06B6D4' }} />,
    'Node.js': <SiNodedotjs className="w-3 h-3" style={{ color: '#339933' }} />,
    'PostgreSQL': <SiPostgresql className="w-3 h-3" style={{ color: '#336791' }} />,
    'Prisma': <SiPrisma className="w-3 h-3" style={{ color: '#E5E7EB' }} />,
    'Stripe': <SiStripe className="w-3 h-3" style={{ color: '#635BFF' }} />,
    'Socket.io': <SiSocketdotio className="w-3 h-3" style={{ color: '#FFFFFF' }} />,
    'Vite': <SiVite className="w-3 h-3" style={{ color: '#646CFF' }} />,
    'Framer Motion': <SiFramer className="w-3 h-3" style={{ color: '#0055FF' }} />,
    'JavaScript': <SiJavascript className="w-3 h-3" style={{ color: '#F7DF1E' }} />,
    'Express.js': <SiExpress className="w-3 h-3" style={{ color: '#FFFFFF' }} />,
    'MongoDB': <SiMongodb className="w-3 h-3" style={{ color: '#47A248' }} />,
    'Chart.js': <SiChartdotjs className="w-3 h-3" style={{ color: '#FF6384' }} />,
  }
  return iconMap[tech] || <div className="w-3 h-3 bg-gray-400 rounded"></div>
}

function ProjectDetails({ project, highlightColor }: ProjectDetailsProps) {
  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      transition={{ duration: 0.4, ease: "easeInOut" }}
      className="space-y-6" // Reduced spacing between sections
    >
      {/* Title & Description */}
      <div className="flex items-start gap-4">
        <div className="w-6 h-px mt-4 shrink-0" style={{ backgroundColor: highlightColor }} />
        <div className="grow">
          <h3 className="text-2xl lg:text-3xl font-bold text-foreground leading-tight">
            {project.title}
          </h3>
          <p className="text-muted-foreground leading-relaxed text-xs mt-3">
            {project.description}
          </p>
        </div>
      </div>

      {/* Features List */}
      <div className="flex items-start gap-4">
        <div className="w-6 shrink-0" /> {/* This empty div acts as a spacer for alignment */}
        <div className="grow space-y-2">
          {project.features.slice(0, 5).map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: 10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className="flex items-start gap-3"
            >
              <span className="font-bold text-xs mt-0.5" style={{ color: highlightColor }}>+</span>
              <span className="text-xs text-muted-foreground leading-relaxed">
                {feature}
              </span>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Technologies */}
      <div className="flex items-start gap-4">
        <div className="w-6 shrink-0" /> {/* Spacer for alignment */}
        <div className="grow">
          <div className="flex flex-wrap gap-2">
            {project.technologies.slice(0, 9).map((tech) => (
              <div
                key={tech}
                className="flex items-center gap-1.5 px-2.5 py-1 bg-neutral-900/60 border border-neutral-700/80 rounded-full text-xs font-medium text-neutral-300 hover:bg-neutral-800/80 transition-colors duration-300 shadow-[inset_0_1px_0_0_rgba(255,255,255,0.07)]"
              >
                {getTechIcon(tech)}
                <span className="truncate">{tech}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </motion.div>
  )
}