import React, { useState } from "react"
import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Map, Grid3X3 } from "lucide-react"
import SkillsRoadmap from "@/components/sections/skills/SkillsRoadmap"
import {
  HeroSection,
  TechnicalSkillsSection,
  SoftSkillsSection,
  LearningGoalsSection,
  CallToActionSection
} from "@/components/sections/skills"

type ViewMode = 'roadmap' | 'traditional'

export default function Skills() {
  const [viewMode, setViewMode] = useState<ViewMode>('roadmap')

  return (
    <div className="relative min-h-screen">
      {/* View Toggle */}
      <div className="absolute top-6 right-6 z-30">
        <Card className="bg-slate-800/50 border-slate-600 backdrop-blur-sm">
          <CardContent className="p-3">
            <div className="flex items-center gap-2">
              <Button
                variant={viewMode === 'roadmap' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('roadmap')}
                className="flex items-center gap-2"
              >
                <Map className="w-4 h-4" />
                Roadmap
              </Button>
              <Button
                variant={viewMode === 'traditional' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('traditional')}
                className="flex items-center gap-2"
              >
                <Grid3X3 className="w-4 h-4" />
                Traditional
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Roadmap View */}
      {viewMode === 'roadmap' && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
        >
          <SkillsRoadmap />
        </motion.div>
      )}

      {/* Traditional View */}
      {viewMode === 'traditional' && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
          className="relative container mx-auto px-6 md:px-8 py-12 md:py-16 max-w-6xl z-10"
        >
          {/* Hero Section */}
          <HeroSection />

          {/* Technical Skills */}
          <TechnicalSkillsSection />

          {/* Soft Skills */}
          <SoftSkillsSection />

          {/* Learning Goals */}
          <LearningGoalsSection />

          {/* Call to Action */}
          <CallToActionSection />
        </motion.div>
      )}
    </div>
  )
}