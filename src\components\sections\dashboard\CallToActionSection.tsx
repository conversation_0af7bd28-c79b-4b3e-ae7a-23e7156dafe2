import { useNavigate } from 'react-router-dom'
import { Button } from '@/components/ui/button'
import { scrollToTopInstant } from '@/components/ScrollToTop'
import { BaseSectionProps } from './types'

export default function CallToActionSection({ className }: BaseSectionProps) {
  const navigate = useNavigate()

  const handleNavigation = (path: string) => {
    scrollToTopInstant()
    navigate(path)
  }

  return (
    <section className={`py-20 lg:py-32 ${className || ''}`}>
      <div className="container mx-auto px-4">
        <div className="relative max-w-4xl mx-auto">
          {/* FloatingNav-style background */}
          <div className="bg-neutral-900/50 backdrop-blur-lg shadow-[inset_0_0_20px_0_rgba(255,255,255,0.08)] rounded-3xl p-12 text-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <h2 className="text-display text-3xl md:text-4xl lg:text-5xl text-foreground">
                  Have a project in mind?
                </h2>
                <p className="text-body text-lg md:text-xl text-muted-foreground max-w-2xl mx-auto mt-6">
                  I'm always open to discussing new projects and opportunities.
                  Let's create something amazing together.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-6 justify-center items-center pt-6">
                {/* Contact Me button - matching HeroSection "Let's Connect" style */}
                <Button
                  onClick={() => handleNavigation('/contact')}
                  className="group relative bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-300 ease-in-out px-8 py-4 text-lg font-semibold border-0 rounded-xl overflow-hidden"
                >
                  {/* Shimmer effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/10 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700 ease-in-out" />
                  <span className="relative z-10">Contact Me</span>
                </Button>

                {/* Learn More About Me button - consistent with other CTAs */}
                <Button
                  onClick={() => handleNavigation('/about')}
                  className="group relative bg-neutral-900/50 hover:bg-neutral-800/50 text-white shadow-[inset_0_0_20px_0_rgba(255,255,255,0.08)] hover:shadow-[inset_0_0_25px_0_rgba(255,255,255,0.12)] transition-all duration-300 ease-in-out px-8 py-4 text-lg font-medium border border-neutral-700/50 hover:border-neutral-600/50 rounded-xl backdrop-blur-lg"
                >
                  <span className="relative z-10">Learn More About Me</span>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
